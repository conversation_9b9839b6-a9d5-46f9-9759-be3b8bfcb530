# 效果接口类
class_name EffectInterface
extends RefCounted

# 定义效果系统的标准接口

# 应用效果
func apply(_target) -> void:
	pass

# 移除效果
func remove(_target) -> void:
	pass

# 获取效果描述
func get_description() -> String:
	return "效果"

# 获取效果ID
func get_id() -> String:
	return ""

# 获取效果类型
func get_type() -> int:
	return -1

# 获取效果值
func get_value():
	return null

# 检查效果是否可堆叠
func is_stackable() -> bool:
	return false

# 获取效果持续时间 (-1表示永久)
func get_duration() -> float:
	return -1.0

# 检查效果是否已过期
func is_expired() -> bool:
	return false

# 更新效果 (用于有持续时间的效果)
func update(_delta: float) -> bool:
	return false  # 返回true表示效果已过期 