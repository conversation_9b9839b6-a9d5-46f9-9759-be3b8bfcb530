[docks]

dock_3_selected_tab_idx=0
dock_4_selected_tab_idx=0
dock_5_selected_tab_idx=0
dock_floating={}
dock_filesystem_h_split_offset=240
dock_filesystem_v_split_offset=0
dock_filesystem_display_mode=0
dock_filesystem_file_sort=0
dock_filesystem_file_list_display_mode=1
dock_filesystem_selected_paths=PackedStringArray("res://")
dock_filesystem_uncollapsed_paths=PackedStringArray("Favorites", "res://")
dock_node_current_tab=0
dock_history_include_scene=true
dock_history_include_global=true
dock_bottom=[]
dock_closed=[]
dock_split_2=0
dock_split_3=0
dock_hsplit_1=0
dock_hsplit_2=270
dock_hsplit_3=-216
dock_hsplit_4=0
dock_3="Scene,Import"
dock_4="FileSystem"
dock_5="Inspector,Node,History"

[EditorNode]

open_scenes=PackedStringArray("res://scenes/player/player.tscn")
current_scene="res://scenes/player/player.tscn"
center_split_offset=-470
selected_default_debugger_tab_idx=0
selected_main_editor_idx=2
selected_bottom_panel_item=1

[EditorWindow]

screen=0
mode="maximized"
position=Vector2i(0, 23)
size=Vector2i(1024, 1000)

[ScriptEditor]

open_scripts=["res://scenes/powerups/base_power_up.gd", "res://scenes/globals/boundary_manager.gd", "res://scenes/globals/debug_manager.gd", "res://scenes/enemies/enemy_triangle.gd", "res://scenes/globals/game_manager.gd", "res://scenes/ui/health_display.gd", "res://scenes/main/main_game.gd", "res://scenes/globals/ModificationManagerAutoload.gd", "res://scenes/modifications/scripts/modification_data_manager.gd", "res://scenes/modifications/data/modification_data_manager.gd", "res://scenes/modifications/scripts/modification_effect.gd", "res://scenes/ui/modification_screen.gd", "res://scenes/modifications/scripts/modification_system.gd", "res://scenes/modifications/scripts/modification_system_new.gd", "res://scenes/modifications/scripts/modification_visuals_manager.gd", "res://scenes/ui/mod_grid_cell.gd", "res://scenes/ui/mod_inventory_item.gd", "res://scenes/ui/mod_inventory_panel.gd", "res://scenes/modifications/visuals/mod_visual.gd", "res://scenes/modifications/visuals/mod_visual_manager.gd", "res://scenes/ui/new_modification_controller.gd", "res://scenes/modifications/data/new_modification_data.gd", "res://scenes/player/player.gd", "res://scenes/globals/resource_manager.gd", "res://scenes/ui/shield_display.gd", "res://scenes/ui/shield_icon.gd", "res://scenes/globals/signal_utils.gd", "res://scenes/ui/tooltip.gd", "res://scenes/ui/tooltip_manager.gd", "res://scenes/ui/ui_hud.gd", "res://scenes/globals/wave_manager.gd"]
selected_script="res://scenes/modifications/data/modification_data_manager.gd"
open_help=[]
script_split_offset=200
list_split_offset=0
zoom_factor=1.0

[GameView]

floating_window_rect=Rect2i(0, 23, 1920, 1009)
floating_window_screen=0

[ShaderEditor]

open_shaders=[]
split_offset=200
selected_shader=""
text_shader_zoom_factor=1.0

[editor_log]

log_filter_0=true
log_filter_2=true
log_filter_1=true
log_filter_3=true
log_filter_4=true
collapse=false
show_search=true
