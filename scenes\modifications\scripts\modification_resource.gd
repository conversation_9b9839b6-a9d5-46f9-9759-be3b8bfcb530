extends Resource
class_name ModificationResource

# 改装配件资源
# 存储改装配件的基本数据和效果

# 基本属性
var id: String = ""
var name: String = ""
var description: String = ""
var color: Color = Color(0.5, 0.5, 0.5)  # 默认灰色
var icon_path: String = ""

# 效果属性（向后兼容）
var shield_bonus: int = 0
var health_bonus: int = 0
var speed_bonus: float = 0.0
var damage_bonus: float = 0.0

# 效果标签
var effect_tags: Array = []

# 新系统 - 效果列表
var effects: Array = []

# 初始化
func _init(p_id: String = "", p_name: String = "", p_description: String = "", p_color: Color = Color(0.5, 0.5, 0.5)):
	id = p_id
	name = p_name
	description = p_description
	color = p_color

# 添加效果
func add_effect(effect) -> void:
	effects.append(effect)

# 获取所有效果
func get_effects() -> Array:
	return effects

# 获取效果描述文本
func get_effects_description() -> String:
	var desc = ""
	
	if shield_bonus > 0:
		desc += "护盾 +%d\n" % shield_bonus
		
	if health_bonus > 0:
		desc += "生命 +%d\n" % health_bonus
		
	if speed_bonus > 0:
		desc += "速度 +%.0f%%\n" % (speed_bonus * 100)
		
	if damage_bonus > 0:
		desc += "伤害 +%.0f%%\n" % (damage_bonus * 100)
	
	# 添加自定义效果描述
	for effect in effects:
		if effect.has_method("get_description"):
			desc += effect.get_description() + "\n"
	
	return desc.strip_edges()

# 添加效果标签
func add_effect_tag(tag: String) -> void:
	if not effect_tags.has(tag):
		effect_tags.append(tag)

# 检查是否有指定标签
func has_effect_tag(tag: String) -> bool:
	return effect_tags.has(tag)

# 获取配件摘要
func get_summary() -> String:
	return "%s: %s" % [name, description] 