# 项目：几何射击 (Geometric Shooter)

## 1. 项目概述与目标 (Overall Goal)
开发一款基于 Godot Engine 4.4.1 的 2D、俯视角、PC 平台的弹幕射击游戏。玩家控制一个蓝色几何方块移动、自动射击、躲避敌人弹幕。通过收集随机掉落的强化道具 (T, A, S, +) 临时提升能力，并通过击败 Boss 获得永久改装件 (护盾、发射器、召唤物) 进行策略组合与强化。游戏目标是生存、获取高分和最远距离。采用极简主义几何美学，高对比度色彩，强调粒子与光晕特效。
@几何射击 (Geometric Shooter) - 游戏需求详细说明 .txt   文件内有详细的内容可以参考
## 2. 核心功能模块 (大致开发顺序)
- [x] **模块 1:** 项目设置 & 玩家核心 (Godot Setup, Player Scene, Appearance, Basic Movement, *旧版*Auto-Shooting, Basic Bullet)
- [X] **模块 2:** 基础敌人系统 (Basic Triangle Enemy: Scene, Script, HP, Movement, Attack, Bullet, Simple Generation)
- [x] **模块 3:** 核心游戏循环 & 基础 UI (Collision Layers, Player Hit/Death, Game Over, Score, Health, Distance Display)
- [X] **模块 4:** 敌人生成与难度 (Wave Manager Concept, Basic Spawning, Time-based Difficulty - *肉鸽模式基础*)
- [x] **模块 5:** 道具系统 (Item Scenes, Drop Logic, T, A, S, + Effects, HUD Status)
- [x] **模块 6:** 玩家护盾系统 (Shield Logic & Visuals)
- [x] **模块 7:** 改装系统 UI (Modification Screen Scene & Basic UI Interaction - *基础框架*)
- [x] **模块 8:** 玩家核心体验增强 (Player Core Enhancements)

*第一阶段：核心Gameplay增强与核心系统构建*
- [ ] **模块 9:** **改装系统 - 核心逻辑与基础框架 (Modification System - Core Logic & Basic Framework)**
    - [x] 子任务9.1: **配件数据结构定义：** 定义改装件的基础属性（ID, 名称, 描述, 尺寸, 形状, 类型(攻防特核), 质量值, 功率消耗值, 提供的通用被动加成类型与数值, 等级, 升级所需资源等）。
    - [ ] 子任务9.2: **网格系统逻辑：** 9x11网格的实现，配件的放置、旋转、移除逻辑。
    - [ ] 子任务9.3: **连接性规则实现：** 确保配件与核心或其他配件通过共享边连接，禁止悬空。
    - [ ] 子任务9.4: **核心资源系统 (第一部分)：**
        - [ ] 子任务9.4.1: 实现玩家飞船的"最大功率负载"和"最大结构质量"属性。
        - [ ] 子任务9.4.2: 实现"能量核心"和"结构框架/质量核心"这两类核心型配件的基础效果（提供负载/质量上限）。
        - [ ] 子任务9.4.3: 改装界面实时显示并校验当前功率消耗/质量总和是否超出上限。
    - [ ] 子任务9.5: **通用被动加成实现 (硬上限)：** 根据装备的小型/中型/大型配件数量，正确计算并应用通用护盾/生命加成，并实现其数量上限逻辑。
    - [ ] 子任务9.6: **玩家碰撞体动态更新：** 根据已装备配件的形状和位置，实时更新玩家的整体碰撞区域。
- [ ] **模块 10:** **改装系统 - 基础物品效果实现 (第一批 - 攻防特各1-2种) (Modification System - Basic Item Effects Batch 1)**
    - [ ] 子任务10.1: 实现**小型攻击配件**：“小型脉冲炮台”（基础射击）。
    - [ ] 子任务10.2: 实现**小型防御配件**：“附加装甲板”（增加固定HP）或“小型护盾增强器”（加快护盾恢复）。
    - [ ] 子任务10.3: 实现**小型特殊配件**：“材料吸附模块”（自动吸取资源）。
    - [ ] 子任务10.4: *（新增）* **动态地图扩展 (第一级)：** 根据少量配件（例如0-20格）实现从“初始极小地图”（如果设计了比小型更小的初始）到“小型地图”的扩展逻辑（选择Viewport缩放或区域边界调整方式之一进行初步实现）。
- [ ] **模块 11:** **玩家核心技能系统 (Player Core Abilities System)**
    - [ ] 子任务11.1: “维度滑行 (Dimension Sliding)”技能的基础逻辑 (短暂改变碰撞层/掩码, 视觉效果) 与冷却机制。
    - [ ] 子任务11.2: “悖论共鸣 (Paradox Resonance)”技能的基础逻辑 (范围AOE, 对AI敌人施加“逻辑混乱”状态) 与冷却机制。
- [ ] **模块 12:** **Boss 系统框架 & Boss 1 (故事模式序章 - “初级谐振塔”)**
    - [ ] 子任务12.1: 通用Boss AI行为树/状态机框架搭建。
    - [ ] 子任务12.2: Boss血条UI实现在HUD上的显示与更新。
    *   [ ] 子任务12.3: 实现Boss“初级谐振塔”的场景、模型（几何组合）、HP、所有攻击模式、阶段变化及掉落逻辑（掉落基础货币和可能的一个特定小型改装件蓝图）。
- [ ] **模块 13:** **进阶敌人系统 (第一批 - 故事模式早期)**
    *   [ ] 子任务13.1: 实现“几何执行者 - 三角追猎者” (含三连发/聚合碎晶)。
    *   [ ] 子任务13.2: 实现“几何执行者 - 球形卫兵” (含多种阵型移动与攻击)。
    *   [ ] 子任务13.3: 实现“几何执行者 - 五边塑形者” (含Z字移动与扇形射线)。
- [ ] **模块 14:** **资源与进阶系统 (第一部分) (Resources & Progression System - Part 1)**
    *   [ ] 子任务14.1: **材料系统基础：** 实现3-5种核心材料的数据定义与玩家库存管理。敌人掉落材料的逻辑（基础几何碎片为主）。
    *   [ ] 子任务14.2: **游戏货币系统：** 获取（敌人掉落少量、完成简单任务/波次奖励）与全局存储逻辑。HUD显示。
    *   [ ] 子任务14.3: **改装件制造系统 (基础)：** 在改装界面实现基于蓝图和材料制造1级基础配件的功能。初始提供少量基础配件蓝图。
    *   [ ] 子任务14.4: **改装件升级系统 (基础)：** 实现消耗货币和材料提升配件等级（例如1-3级），增强其基础属性或通用被动加成的功能。
    *   [ ] 子任务14.5: 主菜单“升级/机库”界面的UI设计与基础交互框架（用于后续永久升级）。
- [ ] **模块 15:** **AI友军系统 (AI Companion System - 基础)**
    *   [ ] 子任务15.1: AI友军的购买/配置UI（集成到“升级/机库”或准备界面 - 占位符功能）。
    *   [ ] 子任务15.2: 实现一种基础AI友军单位的场景和脚本（简单索敌、移动、射击）。
    *   [ ] 子任务15.3: AI友军指令轮盘UI框架与“自由攻击”指令的初步实现。

*第二阶段：游戏模式核心与内容扩展*
- [ ] **模块 16:** **肉鸽模式 - 核心实现 (Roguelike Mode - Core Implementation)**
    *   [ ] 子任务16.1: 随机星图（节点与路径）生成算法与UI显示。
    *   [ ] 子任务16.2: 不同节点类型（战斗、简单商店、简单事件、Boss占位符）的逻辑框架。
    *   [ ] 子任务16.3: 肉鸽模式特有的UI（星图导航、临时资源显示 - 如果有）。
    *   [ ] 子任务16.4: 单局内强化获取与失效机制，永久资源（货币）的结算逻辑。
    *   [ ] 子任务16.5: *（新增）* **动态地图扩展 (第二级)：** 在肉鸽模式中，根据配件数量或层数，实现到“中型地图”的扩展。
- [ ] **模块 17:** **故事模式 - 核心框架 (Story Mode - Core Framework)**
    *   [ ] 子任务17.1: 关卡切换、关卡完成状态管理与游戏进度保存/读取机制。
    *   [ ] 子任务17.2: 基础对话框系统（显示文本、发言者名称、立绘占位符）。
    *   [ ] 子任务17.3: 简单的剧情选择分支显示与选择结果记录逻辑（占位符，影响少量后续对话）。
    *   [ ] 子任务17.4: 基础的过场播放器（例如，按顺序播放多张静态图片并配以文字）。
- [ ] **模块 18:** **Boss 2 (故事模式第一幕 - “秩序守卫者 - 监察矩阵”)**
    *   [ ] 子任务18.1: 实现Boss“监察矩阵”（卡俄斯原型机）的场景、模型、HP、多阶段攻击模式（协同无人机）、核心暴露机制及掉落（掉落货币、卡俄斯数据片段剧情道具、一个中型改装件蓝图）。
- [ ] **模块 19:** **进阶敌人系统 (第二批 - 物理化/催化剂型敌人与故事中期精英)**
    *   [ ] 子任务19.1: 实现“几何构造体 - 五角堡垒”。
    *   [ ] 子任务19.2: 实现“几何执行者 - 菱形干扰者”。
    *   [ ] 子任务19.3: 实现“纯粹几何体 - 柏拉图卫士” (至少2-3种形态)。
    *   [ ] 子任务19.4: 实现“心灵渗透者”。
- [ ] **模块 20:** **Boss 3 (故事模式第二幕 - 艾拉/里奥 “完美形态 mk.I”)**
    *   [ ] 子任务20.1: 实现Boss“完美形态 mk.I”的场景、模型、HP、优雅的移动模式、“几何秩序场”特殊能力、攻击模式及掉落（掉落货币、剧情道具、一个与“完美几何”相关的改装件蓝图）。
    *   [ ] 子任务20.2: 实现此Boss战中的剧情对话交互（基于对话框系统）。
- [ ] **模块 21:** **改装系统 - 进阶物品效果与协同效应 (第一批) (Modification System - Advanced Effects & Synergies Batch 1)**
    *   [ ] 子任务21.1: 实现**中型/大型攻击配件**示例： “中型激光发射器”、“大型导弹巢”、“撞角/冲击钻头”。
    *   [ ] 子任务21.2: 实现**中型/大型防御配件**示例：“能量护盾阵列”、“结构应力分散框架”。
    *   [ ] 子任务21.3: 实现**中型/大型特殊配件**示例：“高级索敌雷达”、“隐形力场发生器”（基础版）。
    *   [ ] 子任务21.4: 实现**1-2种简单的“连接线”或“邻近类型”协同效应**（例如，“放大器”+“炮台”的基础增益，或“散热器集群”的简单叠加）。
    *   [ ] 子任务21.5: *（新增）* **动态地图扩展 (第三级)：** 实现到“大型地图”的扩展逻辑。
    *   [ ] 子任务21.6: *（新增）* **可变地图滚动方向：** 实现至少一种非向上滚动的关卡（例如，横向滚动），并调整玩家控制、背景和敌人生成逻辑以适应。

*第三阶段：多人游戏与高级功能*
- [ ] **模块 22:** **多人游戏 - 核心实现 (Multiplayer - Core Implementation)**
    *   [ ] 子任务22.1: Steam P2P网络功能集成 (使用GodotSteam等插件)。
    *   [ ] 子任务22.2: 游戏大厅UI（创建房间、加入房间、密码保护、玩家列表）。
    *   [ ] 子任务22.3: 基础的玩家状态同步（位置、旋转、核心射击动作、生命/护盾值）使用`MultiplayerSpawner`和`MultiplayerSynchronizer`。
- [ ] **模块 23:** **多人游戏 - Gameplay特性实现 (Multiplayer - Gameplay Features)**
    *   [ ] 子任务23.1: 敌人和Boss状态的同步（生成、HP、主要行为状态）。
    *   [ ] 子任务23.2: 道具、货币（小队共享）、改装件掉落与拾取的同步逻辑。
    *   [ ] 子任务23.3: 多人模式下的玩家死亡与复活机制（驾驶舱弹出、拾取、复活）。
    *   [ ] 子任务23.4: 多人游戏难度动态调整逻辑。
- [ ] **模块 24:** **高级UI系统完善与永久进阶 (Advanced UI & Progression Polish)**
    *   [ ] 子任务24.1: 完善开始菜单（模式选择美化、动画效果）。
    *   [ ] 子任务24.2: 游戏前大厅细节（飞船/AI预览、颜色自定义流程）。
    *   [ ] 子任务24.3: **玩家永久属性升级系统**：在“升级/机库”界面完整实现消耗货币提升各项基础属性的功能与UI。
    *   [ ] 子任务24.4: **AI友军系统完善：** 实现AI友军的升级、多种类型（如果设计）及其指令轮盘的完整功能。

*第四阶段：打磨与故事内容填充 (后期)*
- [ ] **模块 25:** **音频系统 (Audio System)**
    *   [ ] 子任务25.1: 背景音乐（BGM）的完整集成、按场景/模式/Boss战的切换逻辑。
    *   [ ] 子任务25.2: 核心音效（SFX）的制作/采购与在游戏各处的精确绑定和混音。
    *   [ ] 子任务25.3: 设置界面中完整的音量控制功能（主音量、BGM、SFX分离）。
- [ ] **模块 26:** **视觉特效 (VFX) 完善与扩展 (VFX Polish & Expansion)**
    *   [ ] 子任务26.1: 完善并优化星空背景、爆炸、命中、护盾等基础特效。
    *   [ ] 子任务26.2: 为所有已实现的敌人、Boss和玩家技能设计并实现独特的几何粒子特效和动画。
    *   [ ] 子任务26.3: 实现故事模式所需的拓扑几何特效（空间扭曲、几何解构/重构等）的最终视觉效果。
    *   [ ] 子任务26.4: *（新增）* **“不完美/混沌”视觉主题体现：** 为相关配件、技能或敌人设计能体现“不稳定”、“随机”或“有机故障感”的视觉特效。
- [ ] **模块 27:** **改装系统 - 深度拓展 (协同、升级分支、催化剂型配件) (Modification System - Deep Dive)**
    *   [ ] 子任务27.1: 实现更多**物理化/催化剂型配件**（如“偏振滤光片”、“动能转化模块”、“几何稳定器”等）及其独特的协同逻辑。
    *   [ ] 子任务27.2: 实现更复杂的**协同效应**（如“区域光环”、“序列组合”、负面协同）。
    *   [ ] 子任务27.3: **配件分支进化系统：** 实现基础配件达到特定等级后，可以选择不同进化方向的UI与逻辑。为部分配件设计至少2个进化分支。
    *   [ ] 子任务27.4: 实现“不稳定/混沌”系列配件及其特殊机制。
- [ ] **模块 28:** **最终Boss战役实现 (故事模式第三幕) (Final Boss Campaign - Story Mode Act 3)**
    *   [ ] 子任务28.1: 实现艾拉/里奥 - “完美形态 mk.II 与 内心回响”Boss战，包括其特殊机制和多阶段，以及与玩家选择相关的对话/行为变化。
    *   [ ] 子任务28.2: 实现最终Boss“调律者”的化身 - “宇宙几何元”，包括其多形态、宇宙法则干涉机制和宏大攻击模式。
    *   [ ] 子任务28.3: 实现可选Boss战：UHDC“铁序上校”。
    *   [ ] 子任务28.4: 实现故事模式后期的精英怪：“莫比乌斯追踪者”、“克莱因构造体”。
- [ ] **模块 29:** **菜单与设置界面最终打磨 (Menus & Settings Final Polish)**
    *   [ ] 子任务29.1: 所有UI界面的视觉风格统一与用户体验优化，包括动画过渡和响应性。
    *   [ ] 子任务29.2: 设置界面中所有选项（显示、控制（含键位绑定）、音频等）的完整功能实现与保存/加载。
- [ ] **模块 30:** **玩家定制化 (后期实现) (Player Customization - Late Implementation)**
    *   [ ] 子任务30.1: (如果决定加入)立绘系统的美术资源导入与UI集成。
    *   [ ] 子任务30.2: (如果决定加入)语音系统的录制/采购与触发逻辑绑定（初期可用占位符或文字提示）。
- [ ] **模块 31:** **开场/过场动画 (后期实现) (Opening/Cutscenes - Late Implementation)**
    *   [ ] 子任务31.1: 实现故事模式的开场动画（基于静态图片序列或简单2D动画）。
    *   [ ] 子任务31.2: 为故事模式中的关键剧情转折点制作并集成过场动画/影像。

*第五阶段：剧情系统完善与最终化 (非常后期)*
- [ ] **模块 32:** **剧情对话系统完善 (Dialogue System Refinement)**
    *   [ ] 子任务32.1: 支持更复杂的对话树逻辑（例如，根据之前的选择、玩家持有的特定物品或已完成的任务，显示不同对话分支）。
    *   [ ] 子任务32.2: 角色表情/立绘根据对话内容和情感动态切换。
    *   [ ] 子任务32.3: 实现“调律者”或“卡俄斯”AI的特殊对话显示效果（几何符号、动态文本、非标准语言的视觉呈现）。
    *   [ ] 子任务32.4: 数据日志/情报查阅界面的完整实现与内容填充。
- [ ] **模块 33:** **剧情事件与选择系统实现 (Story Event & Choice System Implementation)**
    *   [ ] 子任务33.1: 在故事模式中完整实现所有关键剧情选择点及其对应的直接后果（如任务目标变化、NPC反应变化）。
    *   [ ] 子任务33.2: 玩家选择对剧情变量（如NPC关系状态、结局判定变量）的具体影响逻辑的最终实现。
    *   [ ] 子任务33.3: 结局判定系统的最终实现与所有结局（A, B, C, D, E）分支的达成逻辑和结局影像播放。
- [ ] **模块 34:** **故事模式 - 完整流程构建与细节填充 (Story Mode - Full Flow Construction & Detailing)**
    *   [ ] 子任务34.1: 将所有故事模式的关卡、敌人波次、Boss战、剧情对话、过场动画按照最终剧本精确串联起来，形成可完整游玩的、连贯的体验。
    *   [ ] 子任务34.2: 细化每个关卡的脚本事件、环境叙事元素、隐藏要素和节奏控制。
    *   [ ] 子任务34.3: 为故事模式填充所有必要的文本内容（对话、描述、日志等）。
- [ ] **模块 35:** **最终测试、平衡与打磨 (Final Testing, Balancing & Polish)**
    *   [ ] 子任务35.1: 对故事模式（所有结局分支）、肉鸽模式、多人模式进行全面的功能、体验和兼容性测试。
    *   [ ] 子任务35.2: 游戏数值（敌人HP/攻击，玩家武器/技能强度与消耗，改装件效果与资源消耗，货币获取与支出，各模式难度曲线等）的最终平衡调整。
    *   [ ] 子任务35.3: 所有已知Bug的修复和持续的性能优化（特别关注弹幕数量多、同屏敌人多、以及多人模式下的情况）。
    *   [ ] 子任务35.4: 整体游戏体验的打磨，包括操作手感、界面流畅度、音画表现的最终统一和提升，以及新手引导的完善。


## 3. 技术选型 (Tech Stack)
*   开发引擎: Godot Engine 4.4.1 (用户指定)你
*   平台: PC
*   视角: 固定俯视 (Top-Down)
*   编程语言: GDScript
*   核心 AI (开发辅助): Cursor 内置 Claude 3 (Sonnet/Haiku - 请在 Chat 中手动选择)

## 4. 当前焦点任务 (Current Focus)      
**当前模块:** **模块 9:** 改装系统 - 核心逻辑 (Modification System - Core Logic)
**当前子任务:**  - [ ] 子任务9.1: **配件数据结构定义：** 定义改装件的基础属性（ID, 名称, 描述, 尺寸, 形状, 类型(攻防特核), 质量值, 功率消耗值, 提供的通用被动加成类型与数值, 等级, 升级所需资源等）。
## 5. 重要文件参考 (Key Files)
(AI 或你可以在这里添加关键文件的引用和说明)
- `@player.tscn` / `@player.gd`
- `@enemy_triangle.tscn` / `@enemy_triangle.gd`
- `@main_game.tscn` / `@main_game.gd`
- `@ui_hud.tscn` / `@ui_hud.gd`
- `@powerup_T.tscn` / `@powerup_A.tscn` / etc.
- `@player_bullet.tscn` / `@enemy_bullet.tscn`

## 6. AI 执行规则 (Rules for AI)
请严格按照 PROJECT_PLAN.md 中定义的模块顺序（从当前焦点模块开始）进行开发。

一次只专注于实现 当前焦点任务 中指定的模块和一个具体子任务。

开始新模块前，我会确认上个模块或重要阶段完成并更新此文件（修改模块完成状态 [ ] 为 [x]，并更新 当前焦点任务）。

当一个模块包含多个子任务时，请逐个实现。完成一个子任务后请告知我，我会更新子任务状态（如果我在此文件中也列出子任务状态）。

实现复杂模块或子任务时，如果需要，请先提出更细致的步骤分解计划供我确认。

完成一个子任务或在开发过程中遇到任何问题、不明确之处或需要决策的地方，请立即告知我。

请始终参考整个 @PROJECT_PLAN.md 文件以及最新的 @几何射击 (Geometric Shooter) - 游戏需求详细说明 .txt 文件来理解项目的整体背景、目标、当前进展和具体需求。

代码风格请遵循 GDScript 最佳实践（例如，清晰的变量/函数命名，适当的类型提示，模块化代码），并添加必要的注释来解释关键逻辑。

视觉元素（颜色、尺寸、形状等）请尽量精确遵循GDD中的描述。如果GDD没有明确具体数值但有风格描述，请使用符合该风格的合理默认值，或向我询问。对于GDD中标记为“易于实现”的效果，请优先考虑性能和开发效率。