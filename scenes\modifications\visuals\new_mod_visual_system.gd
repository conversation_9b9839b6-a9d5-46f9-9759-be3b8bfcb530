extends Node2D
class_name NewModVisualSystem

# 新的改装配件视觉系统
# 负责在玩家飞船上即时添加/移除配件的视觉模型

signal visual_added(mod_id: String, visual_node: Node2D)
signal visual_removed(mod_id: String)

# 玩家节点引用
var player_node: Node2D
var player_core_position: Vector2

# 已装备的视觉配件
var equipped_visuals: Dictionary = {}

# 网格设置
var grid_cell_size: float = 20.0
var grid_center_offset: Vector2

# 动画设置
var equip_animation_duration: float = 0.3
var unequip_animation_duration: float = 0.2

func _ready():
	print("NewModVisualSystem: 初始化")
	
	# 设置网格中心偏移
	grid_center_offset = Vector2(0, 0)  # 相对于玩家中心

func initialize(player: Node2D):
	"""初始化视觉系统"""
	player_node = player
	player_core_position = Vector2.ZERO
	
	print("NewModVisualSystem: 已连接到玩家节点")

func equip_modification_visual(mod_data: NewModificationData.ModificationPart, grid_position: Vector2i):
	"""装备配件的视觉效果"""
	if not player_node:
		push_error("NewModVisualSystem: 玩家节点未设置")
		return
	
	# 检查是否已经装备
	if equipped_visuals.has(mod_data.id):
		print("NewModVisualSystem: 配件 " + mod_data.id + " 已装备，先移除旧的")
		unequip_modification_visual(mod_data.id)
	
	# 创建视觉节点
	var visual_node = create_modification_visual(mod_data, grid_position)
	if not visual_node:
		push_error("NewModVisualSystem: 无法创建配件视觉: " + mod_data.id)
		return
	
	# 添加到玩家节点
	player_node.add_child(visual_node)
	
	# 记录装备的视觉
	equipped_visuals[mod_data.id] = {
		"node": visual_node,
		"data": mod_data,
		"grid_position": grid_position
	}
	
	# 播放装备动画
	play_equip_animation(visual_node)
	
	# 发送信号
	emit_signal("visual_added", mod_data.id, visual_node)
	
	print("NewModVisualSystem: 已装备配件视觉: " + mod_data.name)

func unequip_modification_visual(mod_id: String):
	"""卸下配件的视觉效果"""
	if not equipped_visuals.has(mod_id):
		print("NewModVisualSystem: 配件 " + mod_id + " 未装备")
		return
	
	var visual_info = equipped_visuals[mod_id]
	var visual_node = visual_info["node"]
	
	# 播放卸下动画
	play_unequip_animation(visual_node)
	
	# 移除记录
	equipped_visuals.erase(mod_id)
	
	# 发送信号
	emit_signal("visual_removed", mod_id)
	
	print("NewModVisualSystem: 已卸下配件视觉: " + mod_id)

func create_modification_visual(mod_data: NewModificationData.ModificationPart, grid_position: Vector2i) -> Node2D:
	"""创建配件的视觉模型"""
	var visual_container = Node2D.new()
	visual_container.name = "ModVisual_" + mod_data.id
	
	# 计算世界位置
	var world_position = grid_to_world_position(grid_position)
	visual_container.position = world_position
	
	# 根据配件类型创建不同的视觉
	match mod_data.type:
		"core":
			create_core_visual(visual_container, mod_data)
		"attack":
			create_attack_visual(visual_container, mod_data)
		"defense":
			create_defense_visual(visual_container, mod_data)
		"special":
			create_special_visual(visual_container, mod_data)
		_:
			create_default_visual(visual_container, mod_data)
	
	return visual_container

func create_core_visual(container: Node2D, mod_data: NewModificationData.ModificationPart):
	"""创建核心型配件视觉"""
	match mod_data.id:
		"basic_energy_core":
			# 能量核心 - 发光的方形核心
			var core_rect = create_geometric_shape("square", mod_data.color, Vector2(30, 30))
			container.add_child(core_rect)
			
			# 添加能量流动效果
			var energy_particles = create_energy_particles(mod_data.color)
			container.add_child(energy_particles)
			
		"structural_frame":
			# 结构框架 - 连接的框架结构
			var frame_lines = create_frame_structure(mod_data.color, mod_data.shape_data)
			container.add_child(frame_lines)
		_:
			create_default_visual(container, mod_data)

func create_attack_visual(container: Node2D, mod_data: NewModificationData.ModificationPart):
	"""创建攻击型配件视觉"""
	match mod_data.id:
		"pulse_cannon":
			# 脉冲炮 - 三角形炮管
			var cannon_shape = create_geometric_shape("triangle", mod_data.color, Vector2(15, 20))
			container.add_child(cannon_shape)
			
		"fractal_replicator":
			# 分形复制器 - 分形图案
			var fractal_pattern = create_fractal_pattern(mod_data.color)
			container.add_child(fractal_pattern)
			
		"heavy_railgun":
			# 重型轨道炮 - 长条形炮管
			var railgun_shape = create_geometric_shape("rectangle", mod_data.color, Vector2(12, 45))
			container.add_child(railgun_shape)
			
		"klein_lens":
			# 克莱因透镜 - 扭曲的环形结构
			var lens_shape = create_klein_lens_visual(mod_data.color)
			container.add_child(lens_shape)
		_:
			create_default_visual(container, mod_data)

func create_defense_visual(container: Node2D, mod_data: NewModificationData.ModificationPart):
	"""创建防御型配件视觉"""
	match mod_data.id:
		"armor_plate":
			# 装甲板 - 厚重的六边形
			var armor_shape = create_geometric_shape("hexagon", mod_data.color, Vector2(18, 18))
			container.add_child(armor_shape)
			
		"shield_generator":
			# 护盾发生器 - 发光的护盾环
			var shield_ring = create_shield_ring(mod_data.color)
			container.add_child(shield_ring)
			
		"resonance_field":
			# 谐振力场 - 莫比乌斯环形护盾
			var resonance_visual = create_mobius_ring(mod_data.color)
			container.add_child(resonance_visual)
		_:
			create_default_visual(container, mod_data)

func create_special_visual(container: Node2D, mod_data: NewModificationData.ModificationPart):
	"""创建特殊型配件视觉"""
	match mod_data.id:
		"targeting_system":
			# 雷达系统 - 旋转的雷达盘
			var radar_dish = create_radar_visual(mod_data.color)
			container.add_child(radar_dish)
			
		"drone_factory":
			# 无人机工厂 - 复杂的制造设施
			var factory_visual = create_factory_visual(mod_data.color)
			container.add_child(factory_visual)
			
		"gravity_core":
			# 引力核心 - 扭曲空间的视觉效果
			var gravity_visual = create_gravity_visual(mod_data.color)
			container.add_child(gravity_visual)
			
		"chaos_shard":
			# 混沌碎片 - 不稳定的几何碎片
			var chaos_visual = create_chaos_visual(mod_data.color)
			container.add_child(chaos_visual)
			
		"mobius_coil":
			# 莫比乌斯线圈 - 扭曲的能量线圈
			var coil_visual = create_mobius_coil_visual(mod_data.color)
			container.add_child(coil_visual)
		_:
			create_default_visual(container, mod_data)

func create_default_visual(container: Node2D, mod_data: NewModificationData.ModificationPart):
	"""创建默认视觉"""
	var default_shape = create_geometric_shape("square", mod_data.color, Vector2(16, 16))
	container.add_child(default_shape)

func create_geometric_shape(shape_type: String, color: Color, size: Vector2) -> Polygon2D:
	"""创建几何形状"""
	var polygon = Polygon2D.new()
	polygon.color = color
	
	match shape_type:
		"square":
			polygon.polygon = PackedVector2Array([
				Vector2(-size.x/2, -size.y/2),
				Vector2(size.x/2, -size.y/2),
				Vector2(size.x/2, size.y/2),
				Vector2(-size.x/2, size.y/2)
			])
		"triangle":
			polygon.polygon = PackedVector2Array([
				Vector2(0, -size.y/2),
				Vector2(size.x/2, size.y/2),
				Vector2(-size.x/2, size.y/2)
			])
		"hexagon":
			var points = PackedVector2Array()
			for i in range(6):
				var angle = i * PI / 3
				points.append(Vector2(cos(angle) * size.x/2, sin(angle) * size.y/2))
			polygon.polygon = points
		"rectangle":
			polygon.polygon = PackedVector2Array([
				Vector2(-size.x/2, -size.y/2),
				Vector2(size.x/2, -size.y/2),
				Vector2(size.x/2, size.y/2),
				Vector2(-size.x/2, size.y/2)
			])
	
	return polygon

func create_energy_particles(color: Color) -> CPUParticles2D:
	"""创建能量粒子效果"""
	var particles = CPUParticles2D.new()
	particles.emitting = true
	particles.amount = 20
	particles.lifetime = 2.0
	particles.emission_shape = CPUParticles2D.EMISSION_SHAPE_SPHERE
	particles.emission_sphere_radius = 8.0
	particles.direction = Vector2(0, -1)
	particles.initial_velocity_min = 10.0
	particles.initial_velocity_max = 30.0
	particles.scale_amount_min = 0.5
	particles.scale_amount_max = 1.0
	particles.color = color
	
	return particles

func create_frame_structure(color: Color, shape_data: Array) -> Node2D:
	"""创建框架结构"""
	var frame_container = Node2D.new()
	
	for i in range(shape_data.size()):
		var pos = shape_data[i] as Vector2i
		var frame_piece = create_geometric_shape("square", color, Vector2(8, 8))
		frame_piece.position = Vector2(pos.x * 10, pos.y * 10)
		frame_container.add_child(frame_piece)
	
	return frame_container

func create_fractal_pattern(color: Color) -> Node2D:
	"""创建分形图案"""
	var fractal_container = Node2D.new()
	
	# 创建简单的分形三角形
	for i in range(3):
		var triangle = create_geometric_shape("triangle", color, Vector2(8, 8))
		var angle = i * 2 * PI / 3
		triangle.position = Vector2(cos(angle) * 6, sin(angle) * 6)
		triangle.rotation = angle
		fractal_container.add_child(triangle)
	
	return fractal_container

func create_shield_ring(color: Color) -> Line2D:
	"""创建护盾环"""
	var ring = Line2D.new()
	ring.width = 3.0
	ring.default_color = color
	ring.closed = true
	
	# 创建圆形
	var points = PackedVector2Array()
	for i in range(16):
		var angle = i * 2 * PI / 16
		points.append(Vector2(cos(angle) * 12, sin(angle) * 12))
	ring.points = points
	
	return ring

func create_mobius_ring(color: Color) -> Node2D:
	"""创建莫比乌斯环"""
	var mobius_container = Node2D.new()
	
	# 简化的莫比乌斯环表示
	var ring1 = create_shield_ring(color)
	var ring2 = create_shield_ring(color)
	ring2.scale = Vector2(0.7, 0.7)
	ring2.rotation = PI / 4
	
	mobius_container.add_child(ring1)
	mobius_container.add_child(ring2)
	
	return mobius_container

func create_radar_visual(color: Color) -> Node2D:
	"""创建雷达视觉"""
	var radar_container = Node2D.new()
	
	# 雷达盘
	var dish = create_geometric_shape("hexagon", color, Vector2(20, 20))
	radar_container.add_child(dish)
	
	# 旋转的扫描线
	var scan_line = Line2D.new()
	scan_line.width = 2.0
	scan_line.default_color = color
	scan_line.points = PackedVector2Array([Vector2.ZERO, Vector2(0, -15)])
	radar_container.add_child(scan_line)
	
	# 添加旋转动画
	var tween = create_tween()
	tween.set_loops()
	tween.tween_property(scan_line, "rotation", 2 * PI, 2.0)
	
	return radar_container

func create_factory_visual(color: Color) -> Node2D:
	"""创建工厂视觉"""
	var factory_container = Node2D.new()
	
	# 主体结构
	var main_body = create_geometric_shape("rectangle", color, Vector2(25, 15))
	factory_container.add_child(main_body)
	
	# 制造臂
	for i in range(2):
		var arm = create_geometric_shape("rectangle", color, Vector2(8, 3))
		arm.position = Vector2((i - 0.5) * 15, -8)
		factory_container.add_child(arm)
	
	return factory_container

func create_gravity_visual(color: Color) -> Node2D:
	"""创建引力视觉"""
	var gravity_container = Node2D.new()
	
	# 中心核心
	var core = create_geometric_shape("hexagon", color, Vector2(12, 12))
	gravity_container.add_child(core)
	
	# 扭曲环
	for i in range(3):
		var ring = create_shield_ring(color)
		ring.scale = Vector2(0.5 + i * 0.3, 0.5 + i * 0.3)
		ring.modulate.a = 0.7 - i * 0.2
		gravity_container.add_child(ring)
	
	return gravity_container

func create_chaos_visual(color: Color) -> Node2D:
	"""创建混沌视觉"""
	var chaos_container = Node2D.new()
	
	# 不规则碎片
	for i in range(5):
		var shard = create_geometric_shape("triangle", color, Vector2(6, 8))
		shard.position = Vector2(randf_range(-8, 8), randf_range(-8, 8))
		shard.rotation = randf() * 2 * PI
		chaos_container.add_child(shard)
	
	return chaos_container

func create_mobius_coil_visual(color: Color) -> Node2D:
	"""创建莫比乌斯线圈视觉"""
	var coil_container = Node2D.new()
	
	# 扭曲的线圈
	var coil = Line2D.new()
	coil.width = 4.0
	coil.default_color = color
	
	var points = PackedVector2Array()
	for i in range(20):
		var t = i / 19.0
		var x = cos(t * 4 * PI) * (8 + 4 * cos(t * 2 * PI))
		var y = sin(t * 2 * PI) * 4
		points.append(Vector2(x, y))
	coil.points = points
	
	coil_container.add_child(coil)
	
	return coil_container

func create_klein_lens_visual(color: Color) -> Node2D:
	"""创建克莱因透镜视觉"""
	var lens_container = Node2D.new()
	
	# 透镜主体
	var lens = create_geometric_shape("hexagon", color, Vector2(16, 12))
	lens_container.add_child(lens)
	
	# 光学元件
	var optical_element = create_geometric_shape("square", color, Vector2(8, 8))
	optical_element.position = Vector2(0, 0)
	optical_element.modulate.a = 0.7
	lens_container.add_child(optical_element)
	
	return lens_container

func play_equip_animation(visual_node: Node2D):
	"""播放装备动画"""
	# 初始状态
	visual_node.scale = Vector2.ZERO
	visual_node.modulate.a = 0.0
	
	# 动画到最终状态
	var tween = create_tween()
	tween.parallel().tween_property(visual_node, "scale", Vector2.ONE, equip_animation_duration)
	tween.parallel().tween_property(visual_node, "modulate:a", 1.0, equip_animation_duration)
	
	# 添加弹性效果
	tween.tween_property(visual_node, "scale", Vector2(1.1, 1.1), 0.1)
	tween.tween_property(visual_node, "scale", Vector2.ONE, 0.1)

func play_unequip_animation(visual_node: Node2D):
	"""播放卸下动画"""
	var tween = create_tween()
	tween.parallel().tween_property(visual_node, "scale", Vector2.ZERO, unequip_animation_duration)
	tween.parallel().tween_property(visual_node, "modulate:a", 0.0, unequip_animation_duration)
	
	# 动画完成后移除节点
	tween.tween_callback(visual_node.queue_free)

func grid_to_world_position(grid_pos: Vector2i) -> Vector2:
	"""将网格坐标转换为世界坐标"""
	return Vector2(
		grid_pos.x * grid_cell_size + grid_center_offset.x,
		grid_pos.y * grid_cell_size + grid_center_offset.y
	)

func get_equipped_visuals() -> Dictionary:
	"""获取已装备的视觉配件"""
	return equipped_visuals

func clear_all_visuals():
	"""清除所有视觉配件"""
	for mod_id in equipped_visuals.keys():
		unequip_modification_visual(mod_id)
