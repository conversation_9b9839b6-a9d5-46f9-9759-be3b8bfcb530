extends Node

# 作弊码管理器 - 处理特殊输入序列和作弊功能

# Konami Code序列
const KONAMI_CODE = [
	KEY_UP, KEY_UP,
	KEY_DOWN, KEY_DOWN,
	KEY_LEFT, KEY_RIGHT,
	KEY_LEFT, KEY_RIGHT,
	KEY_B, KEY_A
]

# 状态变量
var input_sequence = []  # 记录玩家输入序列
var max_sequence_time = 2.0  # 输入超时时间（秒）
var last_input_time = 0.0  # 上次输入时间
var god_mode_active = false  # 无敌模式状态

# 信号
signal god_mode_activated  # 无敌模式激活信号

func _ready():
	# 初始化
	reset_input_sequence()

func _process(delta):
	# 检查输入序列是否超时
	if input_sequence.size() > 0:
		if Time.get_ticks_msec() / 1000.0 - last_input_time > max_sequence_time:
			reset_input_sequence()

# 处理输入事件
func _input(event):
	if event is InputEventKey and event.pressed:
		process_key_input(event.keycode)

# 处理按键输入
func process_key_input(keycode):
	# 如果无敌模式已激活，不再处理输入序列
	if god_mode_active:
		return
		
	# 更新输入序列
	input_sequence.append(keycode)
	last_input_time = Time.get_ticks_msec() / 1000.0
	
	# 保持序列长度不超过Konami Code
	if input_sequence.size() > KONAMI_CODE.size():
		input_sequence.pop_front()
	
	# 检查是否匹配Konami Code
	check_konami_code()

# 检查是否输入了Konami Code
func check_konami_code():
	# 序列长度必须匹配
	if input_sequence.size() != KONAMI_CODE.size():
		return
	
	# 检查每个按键是否匹配
	var match_found = true
	for i in range(KONAMI_CODE.size()):
		if input_sequence[i] != KONAMI_CODE[i]:
			match_found = false
			break
	
	# 如果匹配成功，激活无敌模式
	if match_found:
		activate_god_mode()

# 激活无敌模式
func activate_god_mode():
	god_mode_active = true
	print("CheatManager: God Mode Activated!")
	
	# 确保发出信号
	god_mode_activated.emit()
	print("CheatManager: Signal emitted!")

# 重置输入序列
func reset_input_sequence():
	input_sequence.clear()

# 获取无敌状态
func is_god_mode_active():
	return god_mode_active

# 重置作弊状态（用于游戏重新开始）
func reset_cheats():
	god_mode_active = false
	reset_input_sequence() 