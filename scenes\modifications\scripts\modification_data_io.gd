class_name ModificationDataIO
extends Node

# 配件数据文件路径
const MODIFICATIONS_FILE_PATH = "user://modifications.json"

# 将所有配件数据保存到JSON文件
static func save_modifications(modifications: Dictionary) -> bool:
	var file_data = {}
	
	# 将每个配件转换为字典
	for mod_id in modifications:
		var mod = modifications[mod_id]
		file_data[mod_id] = mod.to_dict()
	
	# 将字典转换为JSON字符串
	var json_string = JSON.stringify(file_data, "  ")
	
	# 保存到文件
	var file = FileAccess.open(MODIFICATIONS_FILE_PATH, FileAccess.WRITE)
	if file:
		file.store_string(json_string)
		file.close()
		print("配件数据已保存到: " + MODIFICATIONS_FILE_PATH)
		return true
	else:
		print("保存配件数据失败: " + str(FileAccess.get_open_error()))
		return false

# 从JSON文件加载所有配件数据
static func load_modifications() -> Dictionary:
	var modifications = {}
	
	# 检查文件是否存在
	if not FileAccess.file_exists(MODIFICATIONS_FILE_PATH):
		print("配件数据文件不存在: " + MODIFICATIONS_FILE_PATH)
		return modifications
	
	# 打开并读取文件
	var file = FileAccess.open(MODIFICATIONS_FILE_PATH, FileAccess.READ)
	if not file:
		print("打开配件数据文件失败: " + str(FileAccess.get_open_error()))
		return modifications
	
	# 读取JSON字符串
	var json_string = file.get_as_text()
	file.close()
	
	# 解析JSON字符串
	var json = JSON.new()
	var error = json.parse(json_string)
	if error != OK:
		print("解析JSON失败: " + json.get_error_message() + " at line " + str(json.get_error_line()))
		return modifications
	
	# 将JSON数据转换为配件对象
	var file_data = json.data
	for mod_id in file_data:
		var mod_data = file_data[mod_id]
		var mod = ModificationResource.new()
		mod.from_dict(mod_data)
		modifications[mod_id] = mod
	
	print("从文件加载了 " + str(modifications.size()) + " 个配件")
	return modifications 