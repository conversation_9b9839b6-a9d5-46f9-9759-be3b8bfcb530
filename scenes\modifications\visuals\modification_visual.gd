extends Node2D
class_name ModVisualComponent

# 改装配件的可视化组件
# 负责在玩家角色上显示改装配件的视觉效果

# 信号
signal visual_ready
signal visual_removed

# 配件数据
var modification_data = null

# 视觉组件
var visual_node = null

# 常量定义
const PART_SCALE_FACTOR = 0.7

# 初始化
func _ready():
	pass

# 设置配件数据
func set_modification_data(mod_data) -> void:
	modification_data = mod_data
	update_visual()

# 更新视觉效果
func update_visual() -> void:
	# 清除现有视觉效果
	if visual_node:
		visual_node.queue_free()
		visual_node = null
	
	if not modification_data:
		return
	
	# 创建新的视觉效果
	visual_node = create_visual_for_modification(modification_data)
	if visual_node:
		add_child(visual_node)
		emit_signal("visual_ready")

# 根据配件创建视觉效果
func create_visual_for_modification(mod_data) -> Node:
	# 这里根据配件类型创建不同的视觉效果
	# 简单示例：创建一个彩色方块
	var visual = ColorRect.new()
	visual.color = mod_data.color if mod_data.has("color") else Color(0.5, 0.5, 0.5)
	
	# 应用尺寸缩放因子
	var base_size = Vector2(20, 20)
	visual.size = base_size * PART_SCALE_FACTOR
	visual.position = -visual.size / 2  # 居中
	
	# 如果有自定义图标，可以使用Sprite
	if mod_data.has("icon_path") and mod_data.icon_path != "":
		var sprite = Sprite2D.new()
		sprite.texture = load(mod_data.icon_path)
		sprite.scale = Vector2.ONE * PART_SCALE_FACTOR
		visual.add_child(sprite)
	
	return visual

# 获取视觉节点
func get_visual_node() -> Node:
	return visual_node

# 移除视觉效果
func remove() -> void:
	if visual_node:
		visual_node.queue_free()
		visual_node = null
	
	emit_signal("visual_removed")
	queue_free() 