extends Node2D

func _ready():
	# 测试基本的ModificationResource功能
	test_basic_modification_resource()
	
	# 测试全局ModificationManager
	test_global_modification_manager()
	
	# 测试配件数据的保存和加载
	test_modification_data_io()

# 测试基本的ModificationResource功能
func test_basic_modification_resource():
	print("===== 测试基本的ModificationResource功能 =====")
	
	# 创建测试配件
	var test_mod = ModificationResource.new()
	test_mod.id = "test_mod_01"
	test_mod.name = "测试配件"
	test_mod.description = "这是一个用于测试的配件"
	test_mod.color = Color.BLUE
	test_mod.type = ModificationResource.Type.OFFENSIVE
	test_mod.shape_data = [[0,0], [1,0], [0,1]]  # L形状
	test_mod.mass_value = 2.5
	test_mod.power_consumption = 3.0
	test_mod.level = 1
	
	# 打印原始形状
	print("原始形状:")
	print_shape(test_mod.get_current_shape())
	
	# 旋转并打印
	test_mod.rotate_shape()
	print("\n旋转90度:")
	print_shape(test_mod.get_current_shape())
	
	test_mod.rotate_shape()
	print("\n旋转180度:")
	print_shape(test_mod.get_current_shape())
	
	test_mod.rotate_shape()
	print("\n旋转270度:")
	print_shape(test_mod.get_current_shape())
	
	# 测试尺寸分类
	print("\n配件尺寸类别: " + str(test_mod.get_size_category()))
	
	# 测试序列化和反序列化
	var dict_data = test_mod.to_dict()
	print("\n序列化为字典:")
	print(dict_data)
	
	var new_mod = ModificationResource.new()
	new_mod.from_dict(dict_data)
	print("\n反序列化后:")
	print("ID: " + new_mod.id)
	print("名称: " + new_mod.name)
	print("类型: " + str(new_mod.type))

# 测试全局ModificationManager
func test_global_modification_manager():
	print("\n\n===== 测试全局ModificationManager =====")
	
	# 获取全局ModificationManager实例
	var mod_manager = ModificationManager.get_manager()
	
	# 获取所有配件
	var all_mods = mod_manager.get_all_modifications()
	print("已加载配件总数: " + str(all_mods.size()))
	
	# 获取特定类型的配件
	var offensive_mods = mod_manager.get_modifications_by_type(ModificationResource.Type.OFFENSIVE)
	print("攻击型配件数量: " + str(offensive_mods.size()))
	
	# 获取特定ID的配件
	var pulse_turret = mod_manager.get_modification("small_pulse_turret")
	if pulse_turret:
		print("\n小型脉冲炮台配件信息:")
		print("名称: " + pulse_turret.name)
		print("描述: " + pulse_turret.description)
		print("质量值: " + str(pulse_turret.mass_value))
		print("功率消耗: " + str(pulse_turret.power_consumption))
		print("形状:")
		print_shape(pulse_turret.get_current_shape())
	
	# 创建配件实例
	var armor_instance = mod_manager.create_modification_instance("armor_plate")
	if armor_instance:
		print("\n创建附加装甲板实例:")
		print("名称: " + armor_instance.name)
		print("质量值: " + str(armor_instance.mass_value))
		
		# 修改实例属性（不影响原始模板）
		armor_instance.level = 2
		armor_instance.mass_value = 4.0
		
		print("修改后等级: " + str(armor_instance.level))
		print("修改后质量值: " + str(armor_instance.mass_value))
		
		# 检查原始模板是否被修改
		var original = mod_manager.get_modification("armor_plate")
		print("\n原始模板信息:")
		print("等级: " + str(original.level))
		print("质量值: " + str(original.mass_value))

# 测试配件数据的保存和加载
func test_modification_data_io():
	print("\n\n===== 测试配件数据的保存和加载 =====")
	
	# 获取全局ModificationManager实例
	var mod_manager = ModificationManager.get_manager()
	
	# 创建一个新的测试配件
	var test_mod = ModificationResource.new()
	test_mod.id = "test_laser_cannon"
	test_mod.name = "测试激光炮"
	test_mod.description = "用于测试保存和加载功能的配件"
	test_mod.color = Color(1.0, 0.5, 0.0)  # 橙色
	test_mod.type = ModificationResource.Type.OFFENSIVE
	test_mod.shape_data = [[0,0], [1,0], [2,0], [1,1]]  # T形状
	test_mod.mass_value = 3.5
	test_mod.power_consumption = 5.0
	test_mod.level = 1
	test_mod.effects = [{"type": "damage_boost", "value": 3.0}, {"type": "fire_rate", "value": 0.8}]
	
	# 添加到ModificationManager
	print("添加新配件: " + test_mod.name)
	mod_manager.add_modification(test_mod)
	
	# 验证添加是否成功
	var added_mod = mod_manager.get_modification("test_laser_cannon")
	if added_mod:
		print("成功添加配件: " + added_mod.name)
	else:
		print("添加配件失败")
	
	# 修改配件
	added_mod.level = 2
	added_mod.mass_value = 4.0
	print("\n更新配件: " + added_mod.name + " 到等级 " + str(added_mod.level))
	mod_manager.update_modification(added_mod)
	
	# 验证更新是否成功
	var updated_mod = mod_manager.get_modification("test_laser_cannon")
	if updated_mod and updated_mod.level == 2:
		print("成功更新配件: " + updated_mod.name + " 到等级 " + str(updated_mod.level))
	else:
		print("更新配件失败")
	
	# 保存单个配件到特定文件
	var temp_file_path = "user://test_mod.json"
	print("\n保存单个配件到文件: " + temp_file_path)
	var save_result = ModificationDataIO.save_modification(test_mod, temp_file_path)
	print("保存结果: " + str(save_result))
	
	# 从文件加载单个配件
	print("\n从文件加载单个配件: " + temp_file_path)
	var loaded_mod = ModificationDataIO.load_modification(temp_file_path)
	if loaded_mod:
		print("成功加载配件:")
		print("ID: " + loaded_mod.id)
		print("名称: " + loaded_mod.name)
		print("等级: " + str(loaded_mod.level))
		print("形状:")
		print_shape(loaded_mod.get_current_shape())
	else:
		print("加载配件失败")
	
	# 删除测试配件
	print("\n删除测试配件: " + test_mod.id)
	var delete_result = mod_manager.delete_modification("test_laser_cannon")
	print("删除结果: " + str(delete_result))
	
	# 验证删除是否成功
	var deleted_mod = mod_manager.get_modification("test_laser_cannon")
	if deleted_mod == null:
		print("成功删除配件")
	else:
		print("删除配件失败")

# 辅助函数：可视化打印形状
func print_shape(shape_array):
	# 找出形状的边界
	var min_x = 0
	var max_x = 0
	var min_y = 0
	var max_y = 0
	
	for point in shape_array:
		min_x = min(min_x, point[0])
		max_x = max(max_x, point[0])
		min_y = min(min_y, point[1])
		max_y = max(max_y, point[1])
	
	# 创建一个空的网格
	var grid = []
	for y in range(min_y, max_y + 1):
		var row = []
		for x in range(min_x, max_x + 1):
			row.append(".")
		grid.append(row)
	
	# 在网格中标记形状点
	for point in shape_array:
		var x = point[0] - min_x
		var y = point[1] - min_y
		grid[y][x] = "X"
	
	# 打印网格
	for row in grid:
		var line = ""
		for cell in row:
			line += cell + " "
		print(line) 