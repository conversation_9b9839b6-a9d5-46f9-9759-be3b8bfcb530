extends CanvasLayer

# HUD脚本 - 处理游戏界面显示

# 游戏状态
var score: int = 0
var distance: float = 0.0
var distance_timer: float = 0.0
var distance_update_rate: float = 1.0  # 每秒更新距离

# 组件引用
@onready var score_label = $LeftSidebar/ScoreLabel
@onready var distance_label = $LeftSidebar/DistanceLabel
@onready var health_display = $RightSidebar/MarginContainer/BottomContainer/HealthDisplay
@onready var shield_display = $RightSidebar/MarginContainer/BottomContainer/ShieldDisplay
@onready var game_over_container = $GameOverContainer
@onready var final_score_label = $GameOverContainer/PanelContainer/MarginContainer/VBoxContainer/FinalScoreLabel
@onready var final_distance_label = $GameOverContainer/PanelContainer/MarginContainer/VBoxContainer/FinalDistanceLabel

# 道具状态显示
@onready var powerup_container = $LeftSidebar/PowerupContainer
@onready var triple_shot_icon = $LeftSidebar/PowerupContainer/TripleShotIcon
@onready var triple_shot_bar = $LeftSidebar/PowerupContainer/TripleShotIcon/ProgressBar
@onready var auto_aim_icon = $LeftSidebar/PowerupContainer/AutoAimIcon
@onready var auto_aim_bar = $LeftSidebar/PowerupContainer/AutoAimIcon/ProgressBar
@onready var spread_shot_icon = $LeftSidebar/PowerupContainer/SpreadShotIcon
@onready var spread_shot_bar = $LeftSidebar/PowerupContainer/SpreadShotIcon/ProgressBar
@onready var speed_boost_icon = $LeftSidebar/PowerupContainer/SpeedBoostIcon
@onready var speed_boost_bar = $LeftSidebar/PowerupContainer/SpeedBoostIcon/ProgressBar

# 预加载游戏枚举
const GameEnums = preload("res://scenes/globals/game_enums.gd")

# 无敌模式UI
var god_mode_label: Label

func _ready():
	# 初始化HUD
	update_score_display()
	update_distance_display()
	game_over_container.visible = false
	
	# 初始化道具状态显示
	init_powerup_display()
	
	# 创建无敌模式标签
	create_god_mode_label()
	
	# 连接CheatManager信号
	if CheatManager != null:
		CheatManager.god_mode_activated.connect(_on_god_mode_activated)
		
	# 在下一帧更新改装界面设置
	call_deferred("ensure_modification_screen_updated")

# 初始化道具状态显示
func init_powerup_display():
	# 确保道具容器和图标存在
	if not powerup_container or not triple_shot_icon or not auto_aim_icon or not spread_shot_icon or not speed_boost_icon:
		print("警告：道具UI组件未找到，请检查UI_HUD场景")
		return
	
	# 初始状态下隐藏所有道具图标
	triple_shot_icon.visible = false
	auto_aim_icon.visible = false
	spread_shot_icon.visible = false
	speed_boost_icon.visible = false
	
	# 初始化进度条
	if triple_shot_bar:
		triple_shot_bar.value = 0
		triple_shot_bar.max_value = 30.0  # 三连射最大持续时间30秒
		triple_shot_bar.show_percentage = false  # 不显示百分比
		# 添加文本标签显示剩余秒数
		var label = Label.new()
		label.name = "TimeLabel"
		triple_shot_bar.add_child(label)
		label.horizontal_alignment = HorizontalAlignment.HORIZONTAL_ALIGNMENT_CENTER
		label.vertical_alignment = VerticalAlignment.VERTICAL_ALIGNMENT_CENTER
		label.set_anchors_preset(Control.PRESET_FULL_RECT)
		label.add_theme_font_size_override("font_size", 12)
		label.add_theme_color_override("font_color", Color(1, 1, 1, 1))
	
	if auto_aim_bar:
		auto_aim_bar.value = 0
		auto_aim_bar.max_value = 10.0  # 追踪子弹持续时间10秒
		auto_aim_bar.show_percentage = false
		# 添加文本标签显示剩余秒数
		var label = Label.new()
		label.name = "TimeLabel"
		auto_aim_bar.add_child(label)
		label.horizontal_alignment = HorizontalAlignment.HORIZONTAL_ALIGNMENT_CENTER
		label.vertical_alignment = VerticalAlignment.VERTICAL_ALIGNMENT_CENTER
		label.set_anchors_preset(Control.PRESET_FULL_RECT)
		label.add_theme_font_size_override("font_size", 12)
		label.add_theme_color_override("font_color", Color(1, 1, 1, 1))
	
	if spread_shot_bar:
		spread_shot_bar.value = 0
		spread_shot_bar.max_value = 30.0  # 散射最大持续时间30秒
		spread_shot_bar.show_percentage = false
		# 添加文本标签显示剩余秒数
		var label = Label.new()
		label.name = "TimeLabel"
		spread_shot_bar.add_child(label)
		label.horizontal_alignment = HorizontalAlignment.HORIZONTAL_ALIGNMENT_CENTER
		label.vertical_alignment = VerticalAlignment.VERTICAL_ALIGNMENT_CENTER
		label.set_anchors_preset(Control.PRESET_FULL_RECT)
		label.add_theme_font_size_override("font_size", 12)
		label.add_theme_color_override("font_color", Color(1, 1, 1, 1))
	
	if speed_boost_bar:
		speed_boost_bar.value = 0
		speed_boost_bar.max_value = 30.0  # 速度提升最大持续时间30秒
		speed_boost_bar.show_percentage = false
		# 添加文本标签显示剩余秒数
		var label = Label.new()
		label.name = "TimeLabel"
		speed_boost_bar.add_child(label)
		label.horizontal_alignment = HorizontalAlignment.HORIZONTAL_ALIGNMENT_CENTER
		label.vertical_alignment = VerticalAlignment.VERTICAL_ALIGNMENT_CENTER
		label.set_anchors_preset(Control.PRESET_FULL_RECT)
		label.add_theme_font_size_override("font_size", 12)
		label.add_theme_color_override("font_color", Color(1, 1, 1, 1))

func create_god_mode_label():
	# 创建无敌模式标签
	god_mode_label = Label.new()
	add_child(god_mode_label)
	
	# 设置标签属性
	god_mode_label.text = "GOD MODE ACTIVE"
	god_mode_label.horizontal_alignment = HorizontalAlignment.HORIZONTAL_ALIGNMENT_CENTER
	god_mode_label.vertical_alignment = VerticalAlignment.VERTICAL_ALIGNMENT_TOP
	god_mode_label.add_theme_font_size_override("font_size", 24)
	god_mode_label.add_theme_color_override("font_color", Color(1, 0.5, 0, 1))  # 橙色
	
	# 设置标签位置和大小
	var viewport_size = get_viewport().get_visible_rect().size
	god_mode_label.size = Vector2(viewport_size.x, 50)
	god_mode_label.position = Vector2(0, 20)  # 屏幕上方
	
	# 确保标签在屏幕上居中
	god_mode_label.set_anchors_preset(Control.PRESET_TOP_WIDE)
	
	# 初始状态为隐藏
	god_mode_label.visible = false
	
	# 确保标签在最上层显示
	god_mode_label.z_index = 100

func _process(delta):
	# 更新距离计数器
	if not game_over_container.visible:  # 只在游戏进行中更新
		distance_timer += delta
		if distance_timer >= distance_update_rate:
			distance_timer = 0
			distance += 1
			update_distance_display()
	
	# 无敌模式标签闪烁效果
	if god_mode_label.visible:
		var alpha = 0.5 + 0.5 * sin(Time.get_ticks_msec() / 250.0)
		god_mode_label.modulate.a = alpha
		
		# 彩虹颜色效果
		var hue = fmod(Time.get_ticks_msec() / 1000.0, 1.0)
		god_mode_label.add_theme_color_override("font_color", Color.from_hsv(hue, 0.8, 1.0, 1.0))

# 更新分数显示
func update_score_display():
	score_label.text = "SCORE: " + str(score)

# 更新距离显示
func update_distance_display():
	distance_label.text = "DIST: " + str(int(distance)) + " M"

# 更新生命值显示
func update_health_display(health: int):
	health_display.update_health(health)

# 获取健康显示组件
func get_health_display():
	return health_display

# 更新护盾值显示
func update_shield_display(shield: int, max_shield: int):
	if shield_display:
		shield_display.update_shield(shield)
		shield_display.set_max_shield(max_shield)

# 获取护盾显示组件
func get_shield_display():
	return shield_display

# 更新道具状态显示
func update_powerup_status(type: int, active: bool, time_left: float):
	match type:
		GameEnums.PowerUpType.TRIPLE_SHOT:  # Triple Shot
			if active:
				triple_shot_icon.visible = true
				if triple_shot_bar:
					triple_shot_bar.max_value = max(triple_shot_bar.max_value, time_left)
					triple_shot_bar.value = time_left
					# 更新剩余时间文本
					if triple_shot_bar.has_node("TimeLabel"):
						triple_shot_bar.get_node("TimeLabel").text = str(int(time_left)) + "s"
					# 添加闪烁效果，当剩余时间不足5秒时
					if time_left < 5.0:
						var flash_intensity = sin(Time.get_ticks_msec() * 0.01) * 0.5 + 0.5
						triple_shot_icon.modulate = Color(1, 1, 1, 0.5 + flash_intensity * 0.5)
					else:
						triple_shot_icon.modulate = Color(1, 1, 1, 1)
			else:
				triple_shot_icon.visible = false
		
		GameEnums.PowerUpType.AUTO_AIM:  # Auto Aim
			if active:
				auto_aim_icon.visible = true
				if auto_aim_bar:
					auto_aim_bar.max_value = max(auto_aim_bar.max_value, time_left)
					auto_aim_bar.value = time_left
					# 更新剩余时间文本
					if auto_aim_bar.has_node("TimeLabel"):
						auto_aim_bar.get_node("TimeLabel").text = str(int(time_left)) + "s"
					# 添加闪烁效果，当剩余时间不足3秒时
					if time_left < 3.0:
						var flash_intensity = sin(Time.get_ticks_msec() * 0.01) * 0.5 + 0.5
						auto_aim_icon.modulate = Color(1, 1, 1, 0.5 + flash_intensity * 0.5)
					else:
						auto_aim_icon.modulate = Color(1, 1, 1, 1)
			else:
				auto_aim_icon.visible = false
		
		GameEnums.PowerUpType.SPREAD_SHOT:  # Spread Shot
			if active:
				spread_shot_icon.visible = true
				if spread_shot_bar:
					spread_shot_bar.max_value = max(spread_shot_bar.max_value, time_left)
					spread_shot_bar.value = time_left
					# 更新剩余时间文本
					if spread_shot_bar.has_node("TimeLabel"):
						spread_shot_bar.get_node("TimeLabel").text = str(int(time_left)) + "s"
					# 添加闪烁效果，当剩余时间不足5秒时
					if time_left < 5.0:
						var flash_intensity = sin(Time.get_ticks_msec() * 0.01) * 0.5 + 0.5
						spread_shot_icon.modulate = Color(1, 1, 1, 0.5 + flash_intensity * 0.5)
					else:
						spread_shot_icon.modulate = Color(1, 1, 1, 1)
			else:
				spread_shot_icon.visible = false
				
		GameEnums.PowerUpType.SPEED_BOOST:  # Speed Boost
			if active:
				speed_boost_icon.visible = true
				if speed_boost_bar:
					speed_boost_bar.max_value = max(speed_boost_bar.max_value, time_left)
					speed_boost_bar.value = time_left
					# 更新剩余时间文本
					if speed_boost_bar.has_node("TimeLabel"):
						speed_boost_bar.get_node("TimeLabel").text = str(int(time_left)) + "s"
					# 添加闪烁效果，当剩余时间不足5秒时
					if time_left < 5.0:
						var flash_intensity = sin(Time.get_ticks_msec() * 0.01) * 0.5 + 0.5
						speed_boost_icon.modulate = Color(1, 1, 1, 0.5 + flash_intensity * 0.5)
					else:
						speed_boost_icon.modulate = Color(1, 1, 1, 1)
			else:
				speed_boost_icon.visible = false

# 增加分数
func add_score(points: int):
	score += points
	update_score_display()

# 设置距离
func set_distance(new_distance: float):
	distance = new_distance
	update_distance_display()

# 显示游戏结束界面
func show_game_over():
	# 更新最终分数和距离
	final_score_label.text = "FINAL SCORE: " + str(score)
	final_distance_label.text = "DIST: " + str(int(distance)) + " M"
	
	# 如果玩家在无敌模式下，显示作弊提示
	if CheatManager != null and CheatManager.is_god_mode_active():
		var cheat_label = Label.new()
		$GameOverContainer/PanelContainer/MarginContainer/VBoxContainer.add_child(cheat_label)
		cheat_label.text = "CHEAT MODE ACTIVE: KONAMI CODE"
		cheat_label.horizontal_alignment = HorizontalAlignment.HORIZONTAL_ALIGNMENT_CENTER
		cheat_label.add_theme_font_size_override("font_size", 16)
		cheat_label.add_theme_color_override("font_color", Color(1, 0.5, 0, 1))  # 橙色
	
	# 显示游戏结束界面
	game_over_container.visible = true

# 重试按钮点击处理
func _on_retry_button_pressed():
	# 重新加载主游戏场景
	get_tree().reload_current_scene()
	
	# 重置游戏时间缩放（如果之前暂停了游戏）
	Engine.time_scale = 1.0

# 无敌模式激活处理
func _on_god_mode_activated():
	# 显示无敌模式标签
	print("UI: God Mode Label Activated!")
	god_mode_label.visible = true

# 确保修改界面使用最新设置
func ensure_modification_screen_updated():
	# 尝试获取主游戏场景中的修改界面
	var main_game = get_tree().get_first_node_in_group("main_game")
	if main_game:
		var modification_screen = main_game.get_node_or_null("UILayer/ModificationScreen")
		if modification_screen:
			print("正在更新修改界面设置...")
			
			# 尝试更新mod_inventory_panel的设置
			var mod_panel = modification_screen.mod_inventory_panel
			if mod_panel:
				# 调整物品栏的高度和位置
				mod_panel.size.y = 350
				mod_panel.anchors_preset = 15
				mod_panel.anchor_left = 0.0
				mod_panel.anchor_top = 1.0
				mod_panel.anchor_right = 1.0
				mod_panel.anchor_bottom = 1.0
				mod_panel.offset_left = 50.0
				mod_panel.offset_top = -350.0
				mod_panel.offset_right = -50.0
				mod_panel.grow_horizontal = 2
				mod_panel.grow_vertical = 0
				
				# 更新背景样式
				var background = mod_panel.get_node_or_null("Background")
				if background and background.has_method("get_theme_stylebox"):
					var stylebox = background.get_theme_stylebox("panel")
					if stylebox is StyleBoxFlat:
						stylebox.bg_color = Color(0.105882, 0.211765, 0.392157, 0.9)
						stylebox.border_width_left = 2
						stylebox.border_width_top = 2
						stylebox.border_width_right = 2
						stylebox.border_width_bottom = 2
						stylebox.border_color = Color(0.211765, 0.388235, 0.6, 0.8)
						stylebox.corner_radius_top_left = 8
						stylebox.corner_radius_top_right = 8
						stylebox.corner_radius_bottom_right = 8
						stylebox.corner_radius_bottom_left = 8
				
				print("改装仓库设置已更新")
 
