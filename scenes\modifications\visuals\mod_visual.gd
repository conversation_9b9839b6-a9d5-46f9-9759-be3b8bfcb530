extends Node2D
class_name ModVisual

# 改装件视觉表现 - 空实现
# 该脚本已被简化，不再显示改装件的外观

# 改装件资源
var mod_resource = null

# 网格位置
var grid_position: Vector2 = Vector2.ZERO

# 形状多边形引用
@onready var shape_polygon = $ShapePolygon
@onready var outline_polygon = $OutlinePolygon
@onready var glow_effect = $GlowEffect

# 初始化
func _ready():
	print("ModVisual: 空实现已初始化")
	
	# 不执行任何实际操作，但保留最基本的功能以避免错误
	if shape_polygon:
		shape_polygon.visible = false
	
	if outline_polygon:
		outline_polygon.visible = false
	
	if glow_effect:
		glow_effect.energy = 0 
