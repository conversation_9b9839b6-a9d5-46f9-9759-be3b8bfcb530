extends Node
class_name ModSystemLegacy

# 改装系统 - 旧版
# 负责管理玩家的改装配件

# 信号
signal modification_added(mod_id)
signal modification_removed(mod_id)
signal modification_updated(mod_id)

# 玩家引用
var player = null

# 当前装备的改装配件
var equipped_modifications = {}  # 格式: {mod_id: mod_data}

# 改装配件效果
var active_effects = {}  # 格式: {effect_id: effect_data}

# 视觉管理器引用
var visuals_manager = null

# 调试日志
var debug_enabled = true

# 初始化
func _ready():
	# 获取玩家引用
	player = get_parent()
	if not player:
		push_error("ModificationSystem: 无法找到玩家节点!")
		return
	
	# 初始化视觉管理器
	initialize_visuals_manager()
	
	# 加载已保存的改装配件
	load_modifications()

# 初始化视觉管理器
func initialize_visuals_manager():
	# 检查是否已经有视觉管理器
	var existing = player.get_node_or_null("ModificationVisualsManager")
	if existing:
		visuals_manager = existing
		debug_log("使用已存在的视觉管理器")
		return
	
	# 创建视觉管理器
	var visuals_manager_script = load("res://scenes/modifications/scripts/modification_visuals_manager.gd")
	visuals_manager = visuals_manager_script.new()
	visuals_manager.name = "ModificationVisualsManager"
	player.add_child(visuals_manager)
	debug_log("视觉管理器初始化完成")

# 添加改装配件
func add_modification(mod_id: String, mod_data: Dictionary) -> bool:
	# 检查是否已装备
	if equipped_modifications.has(mod_id):
		return false
	
	# 添加改装配件
	equipped_modifications[mod_id] = mod_data
	
	# 应用效果
	apply_modification_effects(mod_id, mod_data)
	
	# 发送信号
	emit_signal("modification_added", mod_id)
	
	# 保存改装配件
	save_modifications()
	
	return true

# 移除改装配件
func remove_modification(mod_id: String) -> bool:
	# 检查是否已装备
	if not equipped_modifications.has(mod_id):
		return false
	
	# 获取改装配件数据
	var mod_data = equipped_modifications[mod_id]
	
	# 移除效果
	remove_modification_effects(mod_id, mod_data)
	
	# 移除改装配件
	equipped_modifications.erase(mod_id)
	
	# 发送信号
	emit_signal("modification_removed", mod_id)
	
	# 保存改装配件
	save_modifications()
	
	return true

# 更新改装配件
func update_modification(mod_id: String, mod_data: Dictionary) -> bool:
	# 检查是否已装备
	if not equipped_modifications.has(mod_id):
		return false
	
	# 获取旧的改装配件数据
	var old_mod_data = equipped_modifications[mod_id]
	
	# 移除旧效果
	remove_modification_effects(mod_id, old_mod_data)
	
	# 更新改装配件
	equipped_modifications[mod_id] = mod_data
	
	# 应用新效果
	apply_modification_effects(mod_id, mod_data)
	
	# 发送信号
	emit_signal("modification_updated", mod_id)
	
	# 保存改装配件
	save_modifications()
	
	return true

# 获取改装配件
func get_modification(mod_id: String) -> Dictionary:
	if equipped_modifications.has(mod_id):
		return equipped_modifications[mod_id]
	return {}

# 获取所有改装配件
func get_all_modifications() -> Dictionary:
	return equipped_modifications

# 应用改装配件效果
func apply_modification_effects(mod_id: String, mod_data: Dictionary) -> void:
	# 检查改装配件是否有效
	if not mod_data:
		return
	
	# 应用护盾加成
	if mod_data.has("shield_bonus") and mod_data.shield_bonus > 0:
		var shield_system = player.get_node_or_null("ShieldSystem")
		if shield_system:
			shield_system.increase_max_shield(mod_data.shield_bonus)
			active_effects[mod_id + "_shield"] = {
				"type": "shield_bonus",
				"value": mod_data.shield_bonus
			}
	
	# 应用生命加成
	if mod_data.has("health_bonus") and mod_data.health_bonus > 0:
		player.increase_max_health(mod_data.health_bonus)
		active_effects[mod_id + "_health"] = {
			"type": "health_bonus",
			"value": mod_data.health_bonus
		}
	
	# 应用速度加成
	if mod_data.has("speed_bonus") and mod_data.speed_bonus > 0:
		player.increase_speed(mod_data.speed_bonus)
		active_effects[mod_id + "_speed"] = {
			"type": "speed_bonus",
			"value": mod_data.speed_bonus
		}
	
	# 应用伤害加成
	if mod_data.has("damage_bonus") and mod_data.damage_bonus > 0:
		player.increase_damage(mod_data.damage_bonus)
		active_effects[mod_id + "_damage"] = {
			"type": "damage_bonus",
			"value": mod_data.damage_bonus
		}

# 移除改装配件效果
func remove_modification_effects(mod_id: String, mod_data: Dictionary) -> void:
	# 检查改装配件是否有效
	if not mod_data:
		return
	
	# 移除护盾加成
	if active_effects.has(mod_id + "_shield"):
		var shield_system = player.get_node_or_null("ShieldSystem")
		if shield_system:
			shield_system.decrease_max_shield(active_effects[mod_id + "_shield"].value)
		active_effects.erase(mod_id + "_shield")
	
	# 移除生命加成
	if active_effects.has(mod_id + "_health"):
		player.decrease_max_health(active_effects[mod_id + "_health"].value)
		active_effects.erase(mod_id + "_health")
		
	# 移除速度加成
	if active_effects.has(mod_id + "_speed"):
		player.decrease_speed(active_effects[mod_id + "_speed"].value)
		active_effects.erase(mod_id + "_speed")
	
	# 移除伤害加成
	if active_effects.has(mod_id + "_damage"):
		player.decrease_damage(active_effects[mod_id + "_damage"].value)
		active_effects.erase(mod_id + "_damage")

# 保存改装配件
func save_modifications() -> void:
	var save_data = {
		"equipped_modifications": equipped_modifications
	}
	
	var file = FileAccess.open("user://modifications_save.json", FileAccess.WRITE)
	if file:
		file.store_string(JSON.stringify(save_data))
		file.close()

# 加载改装配件
func load_modifications() -> void:
	if not FileAccess.file_exists("user://modifications_save.json"):
			return
			
	var file = FileAccess.open("user://modifications_save.json", FileAccess.READ)
	if file:
		var json_string = file.get_as_text()
		file.close()
		
		var json = JSON.new()
		var error = json.parse(json_string)
		if error == OK:
			var save_data = json.data
			
			# 清除当前改装配件
			for mod_id in equipped_modifications.keys():
				remove_modification(mod_id)
			
			# 加载保存的改装配件
			if save_data.has("equipped_modifications"):
				for mod_id in save_data.equipped_modifications:
					add_modification(mod_id, save_data.equipped_modifications[mod_id])
		
# 清除所有改装配件
func clear_all_modifications() -> void:
	# 复制键列表，因为我们会在迭代过程中修改字典
	var mod_ids = equipped_modifications.keys()
	
	for mod_id in mod_ids:
		remove_modification(mod_id)

# 获取特定类型的改装配件
func get_modifications_by_type(type: String) -> Array:
	var result = []
	
	for mod_id in equipped_modifications:
		var mod_data = equipped_modifications[mod_id]
		if mod_data.has("type") and mod_data.type == type:
			result.append(mod_data)
	
	return result

# 获取改装配件总数
func get_modification_count() -> int:
	return equipped_modifications.size()

# 调试日志
func debug_log(message: String):
	if debug_enabled:
		print("ModificationSystem: " + message)

# 更新UI显示
func update_ui():
	if player:
		# 检查player是否是有效的玩家节点
		if not player.has_method("take_damage") or not player.has_method("shoot"):
			push_error("ModificationSystem: update_ui - player不是有效的玩家节点！")
			return
			
		# 检查是否有必要的属性和信号
		if not player.has_signal("shield_changed") or not player.has_signal("health_changed"):
			push_error("ModificationSystem: update_ui - player缺少必要的信号！")
			return
		
		# 发送护盾变化信号，确保UI更新
		player.emit_signal("shield_changed", player.shield_points, player.max_shield_points)
		
		# 发送生命变化信号
		player.emit_signal("health_changed", player.hp)
		
		debug_log("已更新UI显示 - 护盾: %d/%d, 生命: %d/%d" % 
			[player.shield_points, player.max_shield_points, player.hp, player.max_hp])

# 获取当前伤害倍率
func get_damage_multiplier() -> float:
	if player:
		return player.bullet_damage_multiplier
	return 1.0 
