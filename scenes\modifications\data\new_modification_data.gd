extends Resource
class_name NewModificationData

# 新的改装配件数据系统
# 基于需求文档的完整实现

# 改装配件基础数据结构
class ModificationPart:
	var id: String
	var name: String
	var description: String
	var type: String  # "attack", "defense", "special", "core"
	var size_category: String  # "small", "medium", "large"
	var grid_size: Vector2i  # 占用的网格尺寸
	var shape_data: Array  # 形状数据，相对于锚点的偏移
	var color: Color
	var icon_path: String
	var level: int = 1
	var max_level: int = 5
	
	# 属性加成
	var health_bonus: int = 0
	var shield_bonus: int = 0
	var damage_bonus: float = 0.0
	var speed_bonus: float = 0.0
	var power_consumption: int = 0
	var mass_value: int = 0
	
	# 特殊效果
	var special_effects: Array = []
	var upgrade_materials: Dictionary = {}
	var upgrade_cost: int = 0
	
	func _init(data: Dictionary = {}):
		if data.has("id"): id = data["id"]
		if data.has("name"): name = data["name"]
		if data.has("description"): description = data["description"]
		if data.has("type"): type = data["type"]
		if data.has("size_category"): size_category = data["size_category"]
		if data.has("grid_size"): grid_size = data["grid_size"]
		if data.has("shape_data"): shape_data = data["shape_data"]
		if data.has("color"): color = data["color"]
		if data.has("icon_path"): icon_path = data["icon_path"]
		if data.has("level"): level = data["level"]
		if data.has("max_level"): max_level = data["max_level"]
		if data.has("health_bonus"): health_bonus = data["health_bonus"]
		if data.has("shield_bonus"): shield_bonus = data["shield_bonus"]
		if data.has("damage_bonus"): damage_bonus = data["damage_bonus"]
		if data.has("speed_bonus"): speed_bonus = data["speed_bonus"]
		if data.has("power_consumption"): power_consumption = data["power_consumption"]
		if data.has("mass_value"): mass_value = data["mass_value"]
		if data.has("special_effects"): special_effects = data["special_effects"]
		if data.has("upgrade_materials"): upgrade_materials = data["upgrade_materials"]
		if data.has("upgrade_cost"): upgrade_cost = data["upgrade_cost"]

# 预定义的改装配件数据
static func get_all_modifications() -> Array:
	var modifications = []
	
	# 核心型配件
	modifications.append(ModificationPart.new({
		"id": "basic_energy_core",
		"name": "基础能量核心",
		"description": "提供飞船基础能量输出，是所有系统运行的基础",
		"type": "core",
		"size_category": "medium",
		"grid_size": Vector2i(2, 2),
		"shape_data": [Vector2i(0, 0), Vector2i(1, 0), Vector2i(0, 1), Vector2i(1, 1)],
		"color": Color(0.2, 0.6, 1.0),
		"power_consumption": -50,  # 负值表示提供能量
		"mass_value": 8,
		"upgrade_cost": 100
	}))
	
	modifications.append(ModificationPart.new({
		"id": "structural_frame",
		"name": "结构框架",
		"description": "增强飞船结构强度，提供更大的载重能力",
		"type": "core",
		"size_category": "large",
		"grid_size": Vector2i(3, 2),
		"shape_data": [Vector2i(0, 0), Vector2i(1, 0), Vector2i(2, 0), Vector2i(0, 1), Vector2i(2, 1)],
		"color": Color(0.6, 0.6, 0.6),
		"health_bonus": 2,
		"mass_value": 15,
		"special_effects": ["impact_resistance"],
		"upgrade_cost": 150
	}))
	
	# 攻击型配件
	modifications.append(ModificationPart.new({
		"id": "pulse_cannon",
		"name": "小型脉冲炮台",
		"description": "向鼠标方向发射标准能量脉冲",
		"type": "attack",
		"size_category": "small",
		"grid_size": Vector2i(1, 1),
		"shape_data": [Vector2i(0, 0)],
		"color": Color(1.0, 0.3, 0.3),
		"damage_bonus": 0.5,
		"power_consumption": 10,
		"mass_value": 3,
		"special_effects": ["pulse_shot"],
		"upgrade_cost": 80
	}))
	
	modifications.append(ModificationPart.new({
		"id": "fractal_replicator",
		"name": "分形弹头复制器",
		"description": "子弹飞行一段距离后分裂成多个小弹头",
		"type": "attack",
		"size_category": "medium",
		"grid_size": Vector2i(2, 1),
		"shape_data": [Vector2i(0, 0), Vector2i(1, 0)],
		"color": Color(1.0, 0.6, 0.0),
		"damage_bonus": 0.3,
		"power_consumption": 15,
		"mass_value": 5,
		"special_effects": ["fractal_split"],
		"upgrade_cost": 120
	}))
	
	modifications.append(ModificationPart.new({
		"id": "heavy_railgun",
		"name": "重型轨道炮",
		"description": "发射高威力的穿透性弹丸，但射速较慢",
		"type": "attack",
		"size_category": "large",
		"grid_size": Vector2i(1, 3),
		"shape_data": [Vector2i(0, 0), Vector2i(0, 1), Vector2i(0, 2)],
		"color": Color(0.8, 0.2, 0.8),
		"damage_bonus": 1.5,
		"power_consumption": 25,
		"mass_value": 12,
		"special_effects": ["piercing_shot", "slow_fire_rate"],
		"upgrade_cost": 200
	}))
	
	# 防御型配件
	modifications.append(ModificationPart.new({
		"id": "armor_plate",
		"name": "附加装甲板",
		"description": "增加飞船的生命值和物理抗性",
		"type": "defense",
		"size_category": "small",
		"grid_size": Vector2i(1, 1),
		"shape_data": [Vector2i(0, 0)],
		"color": Color(0.4, 0.4, 0.4),
		"health_bonus": 1,
		"mass_value": 4,
		"special_effects": ["physical_resistance"],
		"upgrade_cost": 60
	}))
	
	modifications.append(ModificationPart.new({
		"id": "shield_generator",
		"name": "护盾发生器",
		"description": "增加最大护盾层数，提供能量防护",
		"type": "defense",
		"size_category": "medium",
		"grid_size": Vector2i(2, 1),
		"shape_data": [Vector2i(0, 0), Vector2i(1, 0)],
		"color": Color(0.0, 0.8, 1.0),
		"shield_bonus": 2,
		"power_consumption": 12,
		"mass_value": 6,
		"special_effects": ["energy_shield"],
		"upgrade_cost": 100
	}))
	
	modifications.append(ModificationPart.new({
		"id": "resonance_field",
		"name": "谐振力场发生器",
		"description": "创建动态能量力场，可抵挡多次攻击",
		"type": "defense",
		"size_category": "large",
		"grid_size": Vector2i(2, 2),
		"shape_data": [Vector2i(0, 0), Vector2i(1, 0), Vector2i(0, 1), Vector2i(1, 1)],
		"color": Color(0.5, 0.0, 1.0),
		"shield_bonus": 3,
		"power_consumption": 20,
		"mass_value": 10,
		"special_effects": ["resonance_field", "force_field"],
		"upgrade_cost": 180
	}))
	
	# 特殊型配件
	modifications.append(ModificationPart.new({
		"id": "targeting_system",
		"name": "高级索敌雷达",
		"description": "扩大索敌范围，高亮显示敌人弱点",
		"type": "special",
		"size_category": "medium",
		"grid_size": Vector2i(2, 2),
		"shape_data": [Vector2i(0, 0), Vector2i(1, 0), Vector2i(0, 1), Vector2i(1, 1)],
		"color": Color(0.0, 1.0, 0.0),
		"power_consumption": 8,
		"mass_value": 4,
		"special_effects": ["enhanced_targeting", "enemy_highlight"],
		"upgrade_cost": 90
	}))
	
	modifications.append(ModificationPart.new({
		"id": "drone_factory",
		"name": "几何构造工厂",
		"description": "定期生成友方几何构造体协助战斗",
		"type": "special",
		"size_category": "large",
		"grid_size": Vector2i(3, 2),
		"shape_data": [Vector2i(0, 0), Vector2i(1, 0), Vector2i(2, 0), Vector2i(0, 1), Vector2i(1, 1), Vector2i(2, 1)],
		"color": Color(1.0, 1.0, 0.0),
		"power_consumption": 18,
		"mass_value": 8,
		"special_effects": ["spawn_drones", "geometric_constructs"],
		"upgrade_cost": 160
	}))
	
	modifications.append(ModificationPart.new({
		"id": "gravity_core",
		"name": "引力扭曲核心",
		"description": "创建可控引力场，影响敌人和弹幕轨迹",
		"type": "special",
		"size_category": "large",
		"grid_size": Vector2i(3, 3),
		"shape_data": [
			Vector2i(0, 1), Vector2i(1, 0), Vector2i(1, 1), Vector2i(1, 2), Vector2i(2, 1),
			Vector2i(0, 0), Vector2i(2, 0), Vector2i(0, 2), Vector2i(2, 2)
		],
		"color": Color(0.5, 0.0, 0.5),
		"power_consumption": 30,
		"mass_value": 15,
		"special_effects": ["gravity_field", "space_distortion"],
		"upgrade_cost": 250
	}))
	
	# M.AI剧情独特配件
	modifications.append(ModificationPart.new({
		"id": "chaos_shard",
		"name": "卡俄斯碎片",
		"description": "来自卡俄斯AI的混沌能量，提供不可预测的效果",
		"type": "special",
		"size_category": "small",
		"grid_size": Vector2i(1, 1),
		"shape_data": [Vector2i(0, 0)],
		"color": Color(1.0, 0.0, 1.0),
		"damage_bonus": 0.8,
		"power_consumption": 5,
		"mass_value": 2,
		"special_effects": ["chaos_burst", "unpredictable"],
		"upgrade_cost": 300
	}))
	
	modifications.append(ModificationPart.new({
		"id": "mobius_coil",
		"name": "莫比乌斯线圈",
		"description": "基于拓扑几何的能量回路，提升能量效率",
		"type": "special",
		"size_category": "medium",
		"grid_size": Vector2i(3, 1),
		"shape_data": [Vector2i(0, 0), Vector2i(1, 0), Vector2i(2, 0)],
		"color": Color(0.0, 0.8, 0.8),
		"power_consumption": -5,  # 提供额外能量
		"mass_value": 3,
		"special_effects": ["energy_efficiency", "mobius_effect"],
		"upgrade_cost": 180
	}))
	
	modifications.append(ModificationPart.new({
		"id": "klein_lens",
		"name": "克莱因透镜",
		"description": "基于克莱因瓶结构的光学系统，增强武器射程",
		"type": "attack",
		"size_category": "medium",
		"grid_size": Vector2i(2, 1),
		"shape_data": [Vector2i(0, 0), Vector2i(1, 0)],
		"color": Color(0.8, 0.8, 0.0),
		"damage_bonus": 0.4,
		"power_consumption": 8,
		"mass_value": 4,
		"special_effects": ["extended_range", "phase_penetration"],
		"upgrade_cost": 140
	}))
	
	return modifications

# 获取特定类型的配件
static func get_modifications_by_type(type: String) -> Array:
	var all_mods = get_all_modifications()
	var filtered: Array = []
	
	for mod in all_mods:
		if mod.type == type:
			filtered.append(mod)
	
	return filtered

# 获取特定尺寸的配件
static func get_modifications_by_size(size: String) -> Array:
	var all_mods = get_all_modifications()
	var filtered: Array = []
	
	for mod in all_mods:
		if mod.size_category == size:
			filtered.append(mod)
	
	return filtered

# 根据ID获取配件
static func get_modification_by_id(id: String) -> ModificationPart:
	var all_mods = get_all_modifications()
	
	for mod in all_mods:
		if mod.id == id:
			return mod
	
	return null
