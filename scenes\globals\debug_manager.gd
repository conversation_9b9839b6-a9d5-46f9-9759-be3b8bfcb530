extends Node

# 调试管理器
# 用于管理游戏中的调试/开发者模式功能

signal debug_mode_toggled(is_active)  # 调试模式状态变化信号
signal god_mode_activated  # 无敌模式激活信号

# 调试状态
var is_debug_mode_active: bool = false  # 调试模式是否激活
var is_player_invincible: bool = false  # 玩家是否无敌
var is_inertia_comp_enabled: bool = true  # 是否启用惯性补偿

# 预加载道具场景
var powerup_scenes = {
	"triple_shot": null,  # 三连射
	"auto_aim": null,     # 自动瞄准
	"spread_shot": null,  # 散射
	"health_up": null,    # 回血
	"bomb": null,         # 炸弹
	"speed_boost": null   # 速度提升
}

# 调试模式UI
var debug_ui = null

func _ready():
	# 预加载所有道具场景
	powerup_scenes["triple_shot"] = load("res://scenes/powerups/powerup_T.tscn")
	powerup_scenes["auto_aim"] = load("res://scenes/powerups/powerup_A.tscn")
	powerup_scenes["spread_shot"] = load("res://scenes/powerups/powerup_S.tscn")
	powerup_scenes["health_up"] = load("res://scenes/powerups/powerup_health.tscn")
	powerup_scenes["bomb"] = load("res://scenes/powerups/bomb_powerup.tscn")
	powerup_scenes["speed_boost"] = load("res://scenes/powerups/speed_boost_powerup.tscn")
	
	print("调试管理器初始化完成")

# 处理输入事件
func _unhandled_input(event):
	# 检测调试模式切换键 (~键)
	if event is InputEventKey and event.pressed and event.keycode == KEY_QUOTELEFT:
		toggle_debug_mode()
		get_viewport().set_input_as_handled()
		return
	
	# 如果调试模式未激活，不处理其他调试输入
	if not is_debug_mode_active:
		return
	
	# 处理调试功能键
	if event is InputEventKey and event.pressed:
		match event.keycode:
			KEY_1:  # 生成三连射道具
				spawn_powerup("triple_shot")
				get_viewport().set_input_as_handled()
			KEY_2:  # 生成自动瞄准道具
				spawn_powerup("auto_aim")
				get_viewport().set_input_as_handled()
			KEY_3:  # 生成散射道具
				spawn_powerup("spread_shot")
				get_viewport().set_input_as_handled()
			KEY_4:  # 生成回血道具
				spawn_powerup("health_up")
				get_viewport().set_input_as_handled()
			KEY_5:  # 直接激活炸弹效果
				activate_bomb_effect()
				get_viewport().set_input_as_handled()
			KEY_6:  # 生成速度提升道具
				spawn_powerup("speed_boost")
				get_viewport().set_input_as_handled()
			KEY_7:  # 增加分数
				add_score(1000)
				get_viewport().set_input_as_handled()
			KEY_8:  # 切换玩家无敌状态
				toggle_player_invincibility()
				get_viewport().set_input_as_handled()
			KEY_9:  # 清除所有敌人
				clear_all_enemies()
				get_viewport().set_input_as_handled()
			KEY_0:  # 显示玩家状态
				show_player_status()
				get_viewport().set_input_as_handled()
			KEY_C:  # 切换惯性补偿系统
				toggle_inertia_compensation()
				get_viewport().set_input_as_handled()

# 切换调试模式
func toggle_debug_mode():
	is_debug_mode_active = !is_debug_mode_active
	
	if is_debug_mode_active:
		print("调试模式已激活")
		create_debug_ui()
	else:
		print("调试模式已关闭")
		remove_debug_ui()
		
		# 确保关闭调试模式时也关闭无敌状态
		if is_player_invincible:
			toggle_player_invincibility()
	
	# 发送信号
	emit_signal("debug_mode_toggled", is_debug_mode_active)

# 创建调试UI
func create_debug_ui():
	# 如果已存在则不重复创建
	if debug_ui != null:
		return
	
	# 创建简单的调试UI
	debug_ui = Control.new()
	debug_ui.name = "DebugUI"
	debug_ui.set_anchors_preset(Control.PRESET_FULL_RECT)
	
	# 创建一个VBoxContainer来垂直排列所有标签
	var vbox = VBoxContainer.new()
	vbox.name = "DebugLabels"
	vbox.position = Vector2(10, 10)
	vbox.custom_minimum_size = Vector2(300, 0)
	
	# 主调试模式标签
	var main_label = Label.new()
	main_label.name = "DebugModeLabel"
	main_label.text = "调试模式已激活"
	main_label.add_theme_color_override("font_color", Color(1, 0.2, 0.2))
	vbox.add_child(main_label)
	
	# 添加帮助信息
	var help_label = Label.new()
	help_label.name = "HelpLabel"
	help_label.text = "按键说明：\n1: 三连射道具\n2: 自动瞄准道具\n3: 散射道具\n4: 回血道具\n5: 炸弹道具\n6: 速度提升道具\n7: 增加分数\n8: 切换无敌\n9: 清除敌人\n0: 显示玩家状态\nC: 切换惯性补偿"
	help_label.add_theme_color_override("font_color", Color(0.8, 0.8, 0.2))
	vbox.add_child(help_label)
	
	# 添加状态信息
	var status_label = Label.new()
	status_label.name = "StatusLabel"
	status_label.text = "当前状态：正常"
	status_label.add_theme_color_override("font_color", Color(0.2, 1.0, 0.2))
	vbox.add_child(status_label)
	
	# 添加惯性补偿状态标签
	var inertia_label = Label.new()
	inertia_label.name = "InertiaLabel"
	inertia_label.text = "惯性补偿：" + ("开启" if is_inertia_comp_enabled else "关闭")
	inertia_label.add_theme_color_override("font_color", Color(0.2, 0.8, 1.0))
	vbox.add_child(inertia_label)
	
	debug_ui.add_child(vbox)
	
	# 添加到场景
	var root = get_tree().root
	root.add_child(debug_ui)
	
	# 确保UI显示在最上层
	debug_ui.z_index = 100

# 移除调试UI
func remove_debug_ui():
	if debug_ui != null:
		debug_ui.queue_free()
		debug_ui = null

# 生成道具
func spawn_powerup(powerup_type: String):
	# 检查道具类型是否有效
	if not powerup_scenes.has(powerup_type) or powerup_scenes[powerup_type] == null:
		print("错误：无效的道具类型 - ", powerup_type)
		return
	
	# 获取玩家节点
	var player = get_player_node()
	if player == null:
		print("错误：找不到玩家节点")
		return
	
	# 实例化道具
	var powerup_scene = powerup_scenes[powerup_type]
	var powerup = powerup_scene.instantiate()
	
	# 设置道具位置为玩家位置
	powerup.global_position = player.global_position
	
	# 添加到场景
	get_tree().current_scene.add_child(powerup)
	
	print("已生成道具：", powerup_type)

# 增加分数
func add_score(amount: int):
	# 获取分数管理器
	var score_manager = get_node_or_null("/root/ScoreManager")
	
	if score_manager != null and score_manager.has_method("add_score"):
		score_manager.add_score(amount)
		print("已增加分数：", amount)
	else:
		print("警告：无法增加分数，找不到ScoreManager或add_score方法")

# 切换玩家无敌状态
func toggle_player_invincibility():
	is_player_invincible = !is_player_invincible
	
	# 获取玩家节点
	var player = get_player_node()
	if player == null:
		print("错误：找不到玩家节点")
		return
	
	# 设置玩家无敌状态
	if player.has_method("set_god_mode"):
		player.set_god_mode(is_player_invincible)
	elif "god_mode" in player:
		player.god_mode = is_player_invincible
	
	# 视觉提示
	if is_player_invincible:
		print("玩家无敌模式：开启")
		update_debug_ui_status("无敌模式：开启")
	else:
		print("玩家无敌模式：关闭")
		update_debug_ui_status("调试模式已激活")

# 清除所有敌人
func clear_all_enemies():
	var enemies = get_tree().get_nodes_in_group("enemies")
	var count = 0
	
	for enemy in enemies:
		if is_instance_valid(enemy):
			if enemy.has_method("die"):
				enemy.die()  # 使用敌人的die方法以确保正确处理分数和特效
			else:
				enemy.queue_free()
			count += 1
	
	print("已清除敌人：", count, "个")

# 获取玩家节点
func get_player_node():
	# 尝试多种可能的方式找到玩家节点
	
	# 首先检查当前场景中的player组
	var players = get_tree().get_nodes_in_group("player")
	if players.size() > 0:
		return players[0]
	
	# 如果没有找到，尝试直接在场景树中查找
	var current_scene = get_tree().current_scene
	if current_scene:
		var player = current_scene.get_node_or_null("Player")
		if player:
			return player
			
		# 尝试在子节点中查找
		for child in current_scene.get_children():
			if child.name.to_lower().contains("player"):
				return child
	
	# 如果都没找到，返回null
	print("警告：无法找到玩家节点")
	return null

# 更新调试UI状态文本
func update_debug_ui_status(status_text: String):
	if debug_ui != null:
		var labels = debug_ui.get_node_or_null("DebugLabels")
		if labels:
			var main_label = labels.get_node_or_null("DebugModeLabel")
			if main_label != null:
				main_label.text = status_text
				
			var status_label = labels.get_node_or_null("StatusLabel")
			if status_label != null:
				status_label.text = "当前状态：" + status_text 

# 直接激活炸弹效果
func activate_bomb_effect():
	# 获取玩家节点
	var player = get_player_node()
	if player == null:
		print("错误：找不到玩家节点，无法激活炸弹")
		return
	
	# 检查玩家是否有activate_bomb方法
	if player.has_method("activate_bomb"):
		print("直接激活玩家的炸弹效果")
		player.activate_bomb()
		update_debug_ui_status("已激活炸弹效果")
	else:
		print("错误：玩家没有activate_bomb方法")
		update_debug_ui_status("炸弹激活失败")

# 显示玩家状态
func show_player_status():
	# 获取玩家节点
	var player = get_player_node()
	if player == null:
		print("错误：找不到玩家节点，无法显示玩家状态")
		return
	
	# 收集玩家状态信息
	var status_text = "玩家状态:\n"
	
	# 基本状态
	status_text += "位置: (" + str(int(player.global_position.x)) + ", " + str(int(player.global_position.y)) + ")\n"
	
	# 生命值
	if "hp" in player and "max_hp" in player:
		status_text += "生命值: " + str(player.hp) + "/" + str(player.max_hp) + "\n"
	
	# 护盾
	if "shield_points" in player and "max_shield_points" in player:
		status_text += "护盾: " + str(player.shield_points) + "/" + str(player.max_shield_points) + "\n"
	
	# 无敌状态
	if "is_invincible" in player:
		status_text += "无敌状态: " + ("是" if player.is_invincible else "否") + "\n"
	
	if "god_mode" in player:
		status_text += "上帝模式: " + ("开启" if player.god_mode else "关闭") + "\n"
	
	# 移动速度
	if "move_speed" in player:
		status_text += "移动速度: " + str(int(player.move_speed)) + "\n"
	
	# 射击状态
	if "is_auto_shooting" in player:
		status_text += "自动射击: " + ("开启" if player.is_auto_shooting else "关闭") + "\n"
	
	if "fire_rate" in player:
		status_text += "射击间隔: " + str(player.fire_rate) + "秒\n"
	
	# 激活的道具
	if "power_ups_active" in player:
		var active_powerups = []
		for powerup_type in player.power_ups_active.keys():
			if player.power_ups_active[powerup_type]:
				active_powerups.append(str(powerup_type))
		
		if active_powerups.size() > 0:
			status_text += "激活道具: " + ", ".join(active_powerups)
		else:
			status_text += "激活道具: 无"
	
	# 打印到控制台
	print(status_text)
	
	# 更新调试UI
	update_debug_ui_status("已显示玩家状态")
	
	# 创建一个临时的详细状态窗口
	show_detailed_status_window(status_text)

# 显示详细状态窗口
func show_detailed_status_window(status_text: String):
	# 创建一个临时的窗口显示详细状态
	var popup = PopupPanel.new()
	popup.name = "StatusPopup"
	
	var vbox = VBoxContainer.new()
	vbox.custom_minimum_size = Vector2(300, 200)
	
	var label = Label.new()
	label.text = status_text
	vbox.add_child(label)
	
	var close_button = Button.new()
	close_button.text = "关闭"
	close_button.pressed.connect(func(): popup.queue_free())
	vbox.add_child(close_button)
	
	popup.add_child(vbox)
	
	# 添加到场景
	get_tree().root.add_child(popup)
	
	# 设置位置并显示
	popup.position = Vector2(100, 100)
	popup.popup_centered()

# 切换惯性补偿系统状态
func toggle_inertia_compensation():
	is_inertia_comp_enabled = !is_inertia_comp_enabled
	
	# 获取玩家节点
	var player = get_player_node()
	if player == null:
		print("错误：找不到玩家节点")
		return
	
	# 设置玩家惯性补偿状态
	if "inertia_factor" in player and "mouse_compensation_factor" in player:
		if is_inertia_comp_enabled:
			player.inertia_factor = 0.3  # 恢复默认值
			player.mouse_compensation_factor = 0.06  # 恢复默认值
			print("惯性补偿系统：开启")
			update_debug_ui_inertia_status("开启")
		else:
			player.inertia_factor = 0.0  # 禁用惯性补偿
			player.mouse_compensation_factor = 0.0  # 禁用鼠标补偿
			print("惯性补偿系统：关闭")
			update_debug_ui_inertia_status("关闭")
	else:
		print("警告：玩家没有惯性补偿相关属性")

# 更新调试UI中的惯性补偿状态
func update_debug_ui_inertia_status(status_text: String):
	if debug_ui != null:
		var labels = debug_ui.get_node_or_null("DebugLabels")
		if labels:
			var inertia_label = labels.get_node_or_null("InertiaLabel")
			if inertia_label != null:
				inertia_label.text = "惯性补偿：" + status_text 
 
