extends Node
class_name ModificationSystem

# 改装系统管理器 - 简化版
# 负责管理玩家改装件的应用和效果

# 信号
signal modifications_updated()

# 玩家引用
var player_node = null

# 当前装备的配件
var equipped_modifications = {}

# 视觉管理器引用
var visuals_manager = null

# 调试日志
var debug_enabled = true

# 初始化
func _ready():
	# 获取玩家引用
	player_node = get_parent()
	if not player_node:
		push_error("ModificationSystem: 无法找到玩家节点!")
		return
	
	# 初始化视觉管理器
	initialize_visuals_manager()
	
	# 连接改装屏幕信号
	var mod_screen = get_tree().get_first_node_in_group("modification_screen")
	if mod_screen:
		debug_log("找到修改界面: " + mod_screen.name)
		if not mod_screen.is_connected("modification_screen_closed", _on_modification_screen_closed):
			mod_screen.connect("modification_screen_closed", _on_modification_screen_closed)
			debug_log("已连接到改装屏幕关闭信号")
	else:
		debug_log("警告：未找到修改界面，将无法更新玩家改装状态")
		# 延迟尝试再次查找
		await get_tree().create_timer(1.0).timeout
		mod_screen = get_tree().get_first_node_in_group("modification_screen")
		if mod_screen:
			debug_log("延迟找到修改界面: " + mod_screen.name)
			if not mod_screen.is_connected("modification_screen_closed", _on_modification_screen_closed):
				mod_screen.connect("modification_screen_closed", _on_modification_screen_closed)
				debug_log("已连接到改装屏幕关闭信号")
	
	debug_log("初始化完成")

# 初始化视觉管理器
func initialize_visuals_manager():
	# 检查是否已经有视觉管理器
	var existing = player_node.get_node_or_null("ModificationVisualsManager")
	if existing:
		visuals_manager = existing
		debug_log("使用已存在的视觉管理器")
		return
	
	# 创建视觉管理器
	var visuals_manager_script = load("res://scenes/modifications/scripts/modification_visuals_manager.gd")
	visuals_manager = visuals_manager_script.new()
	visuals_manager.name = "ModificationVisualsManager"
	player_node.add_child(visuals_manager)
	debug_log("视觉管理器初始化完成")

# 当改装屏幕关闭时
func _on_modification_screen_closed():
	debug_log("处理改装屏幕关闭事件")
	
	# 获取改装屏幕
	var mod_screen = get_tree().get_first_node_in_group("modification_screen")
	if not mod_screen:
		push_error("ModificationSystem: 无法获取改装屏幕!")
		return
	
	# 获取装备的改装件
	var mods_data = mod_screen.get_equipped_modifications()
	
	# 备份当前装备状态用于对比
	var old_mods = equipped_modifications.duplicate(true)
	
	# 清空之前的装备状态
	equipped_modifications.clear()
	
	# 更新装备的改装件
	equipped_modifications = mods_data
	
	# 打印装备信息
	debug_log("当前装备的改装件数量: " + str(count_equipped_mods()))
	
	# 更新视觉效果
	if visuals_manager:
		visuals_manager.update_visuals(equipped_modifications)
	
	# 应用改装件效果
	apply_modification_effects()
	
	# 发送信号
	emit_signal("modifications_updated")
	
# 用于测试的函数 - 模拟改装屏幕关闭
func _on_modification_screen_closed_test(mods_data):
	debug_log("处理测试改装屏幕关闭事件")
	
	# 清空之前的装备状态
	equipped_modifications.clear()
	
	# 更新装备的改装件
	equipped_modifications = mods_data
	
	# 打印装备信息
	debug_log("当前装备的改装件数量: " + str(count_equipped_mods()))
	
	# 更新视觉效果
	if visuals_manager:
		visuals_manager.update_visuals(equipped_modifications)
	
	# 应用改装件效果
	apply_modification_effects()
	
	# 发送信号
	emit_signal("modifications_updated")
	
# 计算当前装备的改装件数量（不包括玩家位置）
func count_equipped_mods() -> int:
	var count = 0
	for pos_key in equipped_modifications.keys():
		var mod_info = equipped_modifications[pos_key]
		# 跳过玩家位置
		if mod_info.has("is_player") and mod_info["is_player"]:
			continue
		count += 1
	return count

# 应用改装件效果
func apply_modification_effects():
	if not player_node:
		push_error("ModificationSystem: 无法应用改装效果，玩家节点为空!")
		return
	
	# 重置玩家属性到基础值
	reset_player_stats()
	
	# 活跃效果列表
	var active_effects = []
	
	# 获取ModEffectLegacy类引用
	var mod_effect_script = load("res://scenes/modifications/scripts/modification_effect.gd")
	var EffectType = mod_effect_script.get_script_constant_map()["EffectType"]
	
	# 遍历所有装备的改装件
	for pos_key in equipped_modifications.keys():
		var mod_info = equipped_modifications[pos_key]

		# 跳过玩家位置
		if mod_info.has("is_player") and mod_info["is_player"]:
			continue
		
		# 获取配件ID
		var mod_id = mod_info["name"]
		
		# 根据配件ID应用不同效果
		var mod_effect = null
		
		# 创建效果实例
		if mod_id.begins_with("shield"):
			var effect = mod_effect_script.new()
			effect.type = EffectType.SHIELD_BONUS
			effect.value = 1
			effect.id = mod_id
			effect.description = "护盾增强器: 增加1点护盾上限"
			mod_effect = effect
		elif mod_id.begins_with("health"):
			var effect = mod_effect_script.new()
			effect.type = EffectType.HEALTH_BONUS
			effect.value = 1
			effect.id = mod_id
			effect.description = "生命增强器: 增加1点生命上限"
			mod_effect = effect
		elif mod_id.begins_with("speed"):
			var effect = mod_effect_script.new()
			effect.type = EffectType.SPEED_BONUS
			effect.value = 0.1  # 10%速度加成
			effect.id = mod_id
			effect.description = "速度增强器: 增加10%移动速度"
			mod_effect = effect
		elif mod_id.begins_with("damage"):
			var effect = mod_effect_script.new()
			effect.type = EffectType.DAMAGE_BONUS
			effect.value = 0.15  # 15%伤害加成
			effect.id = mod_id
			effect.description = "伤害增强器: 增加15%伤害"
			mod_effect = effect
		elif mod_id.begins_with("fire_rate"):
			var effect = mod_effect_script.new()
			effect.type = EffectType.FIRE_RATE_BONUS
			effect.value = 0.1  # 10%射速加成
			effect.id = mod_id
			effect.description = "射速增强器: 提高10%射速"
			mod_effect = effect
		
		# 应用效果
		if mod_effect:
			mod_effect.apply(player_node)
			active_effects.append(mod_effect)
			debug_log("应用效果: " + mod_effect.get_description())
	
	# 确保UI更新
	update_ui()
	
	debug_log("应用改装效果完成，总共应用了 " + str(active_effects.size()) + " 个效果")

# 重置玩家属性到基础值
func reset_player_stats():
	if player_node:
		# 检查player_node是否是有效的玩家节点
		if not player_node.has_method("take_damage") or not player_node.has_method("shoot"):
			push_error("ModificationSystem: player_node不是有效的玩家节点！")
			return
			
		# 检查是否有必要的属性
		if not player_node.get("max_shield_points") or not player_node.get("max_hp"):
			push_error("ModificationSystem: player_node缺少必要的属性！")
			return
		
		# 记录当前值用于调试
		var old_shield = player_node.max_shield_points
		var old_hp = player_node.max_hp
		var current_shield = player_node.shield_points  # 保存当前护盾值
	
		# 重置护盾上限
		player_node.max_shield_points = GameConstants.PLAYER_MAX_SHIELD
		
		# 保留当前护盾值，但确保不超过新的上限
		player_node.shield_points = min(current_shield, player_node.max_shield_points)
		
		# 重置生命上限
		player_node.max_hp = GameConstants.PLAYER_DEFAULT_HP
		
		# 重置移动速度
		player_node.base_move_speed = GameConstants.PLAYER_DEFAULT_SPEED
		
		# 重置基础射击速率
		player_node.base_fire_rate = GameConstants.PLAYER_FIRE_RATE
		
		# 重置伤害倍率
		player_node.bullet_damage_multiplier = 1.0
		
		debug_log("已重置玩家属性到基础值 - 护盾: %d->%d (当前值:%d), 生命: %d->%d" % 
			[old_shield, player_node.max_shield_points, player_node.shield_points, old_hp, player_node.max_hp])

# 调试日志
func debug_log(message: String):
	if debug_enabled:
		print("ModificationSystem: " + message)

# 更新UI显示
func update_ui():
	if player_node:
		# 检查player_node是否是有效的玩家节点
		if not player_node.has_method("take_damage") or not player_node.has_method("shoot"):
			push_error("ModificationSystem: update_ui - player_node不是有效的玩家节点！")
			return
			
		# 检查是否有必要的属性和信号
		if not player_node.has_signal("shield_changed") or not player_node.has_signal("health_changed"):
			push_error("ModificationSystem: update_ui - player_node缺少必要的信号！")
			return
		
		# 发送护盾变化信号，确保UI更新
		player_node.emit_signal("shield_changed", player_node.shield_points, player_node.max_shield_points)
		
		# 发送生命变化信号
		player_node.emit_signal("health_changed", player_node.hp)
		
		debug_log("已更新UI显示 - 护盾: %d/%d, 生命: %d/%d" % 
			[player_node.shield_points, player_node.max_shield_points, player_node.hp, player_node.max_hp])

# 获取当前伤害倍率
func get_damage_multiplier() -> float:
	if player_node:
		return player_node.bullet_damage_multiplier
	return 1.0
