extends Control
class_name NewModificationController

# 新的改装系统控制器
# 整合改装网格、仓库系统和视觉效果

signal modification_equipped(mod_id: String, grid_position: Vector2i)
signal modification_unequipped(mod_id: String)

# UI组件
@onready var modification_grid = $MainContainer/LeftPanel/GridContainer/GridPanel/ModificationGrid
@onready var inventory_system = $InventorySystem
@onready var info_panel = $MainContainer/RightPanel/InfoContainer/InfoPanel
@onready var close_button = $MainContainer/LeftPanel/HeaderContainer/CloseButton

# 状态显示组件
@onready var health_value = $MainContainer/RightPanel/StatsContainer/StatsPanel/StatsGrid/HealthValue
@onready var shield_value = $MainContainer/RightPanel/StatsContainer/StatsPanel/StatsGrid/ShieldValue
@onready var power_value = $MainContainer/RightPanel/StatsContainer/StatsPanel/StatsGrid/PowerValue
@onready var mass_value = $MainContainer/RightPanel/StatsContainer/StatsPanel/StatsGrid/MassValue

# 信息显示组件
@onready var mod_name_label = $MainContainer/RightPanel/InfoContainer/InfoPanel/InfoContent/ModNameLabel
@onready var mod_desc_label = $MainContainer/RightPanel/InfoContainer/InfoPanel/InfoContent/ModDescLabel
@onready var mod_stats_label = $MainContainer/RightPanel/InfoContainer/InfoPanel/InfoContent/ModStatsLabel

# 系统组件
var visual_system: NewModVisualSystem
var player_node: Node2D

# 改装数据
var equipped_modifications: Dictionary = {}
var selected_modification: Dictionary = {}

# 网格设置
var grid_size = Vector2i(9, 11)  # 9行11列
var grid_center = Vector2i(4, 5)  # 网格中心位置

# 拖拽状态
var is_dragging = false
var drag_preview: Control
var drag_mod_data: Dictionary

func _ready():
	print("NewModificationController: 初始化")
	
	# 连接信号
	setup_signals()
	
	# 初始化网格
	setup_modification_grid()
	
	# 初始化仓库系统
	setup_inventory_system()

func setup_signals():
	"""设置信号连接"""
	# 仓库系统信号
	if inventory_system:
		inventory_system.mod_selected.connect(_on_mod_selected)
		inventory_system.inventory_closed.connect(_on_inventory_closed)

	# 关闭按钮信号
	if close_button:
		close_button.pressed.connect(_on_close_pressed)

func _on_close_pressed():
	"""处理关闭按钮"""
	hide()
	get_tree().paused = false

func setup_modification_grid():
	"""设置改装网格"""
	if not modification_grid:
		modification_grid = create_modification_grid()
		add_child(modification_grid)

func setup_inventory_system():
	"""设置仓库系统"""
	if inventory_system:
		inventory_system.load_modification_data()

func initialize_with_player(player: Node2D):
	"""使用玩家节点初始化"""
	player_node = player
	
	# 创建视觉系统
	visual_system = NewModVisualSystem.new()
	visual_system.name = "ModVisualSystem"
	player.add_child(visual_system)
	visual_system.initialize(player)
	
	# 连接视觉系统信号
	visual_system.visual_added.connect(_on_visual_added)
	visual_system.visual_removed.connect(_on_visual_removed)
	
	print("NewModificationController: 已连接玩家节点")

func create_modification_grid() -> GridContainer:
	"""创建改装网格"""
	var grid = GridContainer.new()
	grid.name = "ModificationGrid"
	grid.columns = grid_size.x
	grid.custom_minimum_size = Vector2(grid_size.x * 40, grid_size.y * 40)
	
	# 创建网格单元格
	for row in range(grid_size.y):
		for col in range(grid_size.x):
			var cell = create_grid_cell(Vector2i(col, row))
			grid.add_child(cell)
	
	return grid

func create_grid_cell(grid_pos: Vector2i) -> Panel:
	"""创建网格单元格"""
	var cell = Panel.new()
	cell.name = "GridCell_" + str(grid_pos.x) + "_" + str(grid_pos.y)
	cell.custom_minimum_size = Vector2(40, 40)
	
	# 设置样式
	var style = StyleBoxFlat.new()
	style.bg_color = Color(0.2, 0.2, 0.3, 0.5)
	style.border_width_left = 1
	style.border_width_top = 1
	style.border_width_right = 1
	style.border_width_bottom = 1
	style.border_color = Color(0.4, 0.4, 0.6, 0.8)
	cell.add_theme_stylebox_override("panel", style)
	
	# 玩家核心位置特殊标记
	if grid_pos == grid_center:
		style.bg_color = Color(0.0, 0.5, 1.0, 0.7)
		var label = Label.new()
		label.text = "核心"
		label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
		label.vertical_alignment = VERTICAL_ALIGNMENT_CENTER
		label.anchors_preset = Control.PRESET_FULL_RECT
		label.add_theme_font_size_override("font_size", 12)
		label.add_theme_color_override("font_color", Color.WHITE)
		cell.add_child(label)
	
	# 连接输入事件
	cell.gui_input.connect(_on_grid_cell_input.bind(grid_pos))
	
	return cell

func _on_grid_cell_input(event: InputEvent, grid_pos: Vector2i):
	"""处理网格单元格输入"""
	if event is InputEventMouseButton and event.pressed:
		if event.button_index == MOUSE_BUTTON_LEFT:
			# 左键点击 - 放置配件
			if selected_modification.size() > 0:
				try_place_modification(selected_modification, grid_pos)
		elif event.button_index == MOUSE_BUTTON_RIGHT:
			# 右键点击 - 移除配件
			try_remove_modification(grid_pos)

func _on_mod_selected(mod_data: Dictionary):
	"""处理配件选择"""
	selected_modification = mod_data
	print("NewModificationController: 选中配件: " + mod_data.get("name", "未知"))

	# 更新信息面板
	update_info_panel(mod_data)

	# 创建拖拽预览
	create_drag_preview(mod_data)

func _on_inventory_closed():
	"""处理仓库关闭"""
	# 清除选择状态
	selected_modification.clear()
	clear_drag_preview()

func try_place_modification(mod_data: Dictionary, grid_pos: Vector2i) -> bool:
	"""尝试放置配件"""
	# 检查是否可以放置
	if not can_place_modification(mod_data, grid_pos):
		print("NewModificationController: 无法在位置 " + str(grid_pos) + " 放置配件")
		return false
	
	# 检查库存
	var mod_id = mod_data.get("id", "")
	if not inventory_system.inventory_data.has(mod_id) or inventory_system.inventory_data[mod_id] <= 0:
		print("NewModificationController: 库存不足: " + mod_id)
		return false
	
	# 放置配件
	place_modification(mod_data, grid_pos)
	
	# 从库存中移除
	inventory_system.remove_modification_from_inventory(mod_id, 1)
	
	# 清除选择
	selected_modification.clear()
	clear_drag_preview()
	
	return true

func try_remove_modification(grid_pos: Vector2i) -> bool:
	"""尝试移除配件"""
	# 查找该位置的配件
	var mod_id = find_modification_at_position(grid_pos)
	if mod_id == "":
		return false
	
	# 移除配件
	remove_modification(mod_id)
	
	# 返回到库存
	inventory_system.add_modification_to_inventory(mod_id, 1)
	
	return true

func can_place_modification(mod_data: Dictionary, grid_pos: Vector2i) -> bool:
	"""检查是否可以放置配件"""
	var shape_data = mod_data.get("shape_data", [])
	
	# 检查所有形状格子
	for offset in shape_data:
		var check_pos = grid_pos + offset
		
		# 检查边界
		if check_pos.x < 0 or check_pos.x >= grid_size.x or check_pos.y < 0 or check_pos.y >= grid_size.y:
			return false
		
		# 检查是否已被占用
		if is_position_occupied(check_pos):
			return false
	
	# 检查连接性（必须与核心或其他配件相邻）
	if not check_connectivity(mod_data, grid_pos):
		return false
	
	return true

func check_connectivity(mod_data: Dictionary, grid_pos: Vector2i) -> bool:
	"""检查配件连接性"""
	var shape_data = mod_data.get("shape_data", [])
	
	for offset in shape_data:
		var check_pos = grid_pos + offset
		
		# 检查四个方向的相邻位置
		var adjacent_positions = [
			check_pos + Vector2i(0, -1),  # 上
			check_pos + Vector2i(0, 1),   # 下
			check_pos + Vector2i(-1, 0),  # 左
			check_pos + Vector2i(1, 0)    # 右
		]
		
		for adj_pos in adjacent_positions:
			# 检查是否是核心位置
			if adj_pos == grid_center:
				return true
			
			# 检查是否有其他配件
			if is_position_occupied(adj_pos):
				return true
	
	return false

func is_position_occupied(grid_pos: Vector2i) -> bool:
	"""检查位置是否被占用"""
	for mod_id in equipped_modifications.keys():
		var mod_info = equipped_modifications[mod_id]
		var mod_grid_pos = mod_info["grid_position"]
		var shape_data = mod_info["data"]["shape_data"]
		
		for offset in shape_data:
			if mod_grid_pos + offset == grid_pos:
				return true
	
	return false

func find_modification_at_position(grid_pos: Vector2i) -> String:
	"""查找指定位置的配件"""
	for mod_id in equipped_modifications.keys():
		var mod_info = equipped_modifications[mod_id]
		var mod_grid_pos = mod_info["grid_position"]
		var shape_data = mod_info["data"]["shape_data"]
		
		for offset in shape_data:
			if mod_grid_pos + offset == grid_pos:
				return mod_id
	
	return ""

func place_modification(mod_data: Dictionary, grid_pos: Vector2i):
	"""放置配件"""
	var mod_id = mod_data.get("id", "")
	
	# 记录装备信息
	equipped_modifications[mod_id] = {
		"data": mod_data,
		"grid_position": grid_pos
	}
	
	# 更新网格视觉
	update_grid_visual(mod_data, grid_pos, true)
	
	# 添加到玩家飞船视觉
	if visual_system:
		var mod_part = create_mod_part_from_dict(mod_data)
		visual_system.equip_modification_visual(mod_part, grid_pos)
	
	# 发送信号
	emit_signal("modification_equipped", mod_id, grid_pos)
	
	print("NewModificationController: 已装备配件: " + mod_data.get("name", "未知"))

func remove_modification(mod_id: String):
	"""移除配件"""
	if not equipped_modifications.has(mod_id):
		return
	
	var mod_info = equipped_modifications[mod_id]
	var mod_data = mod_info["data"]
	var grid_pos = mod_info["grid_position"]
	
	# 移除装备记录
	equipped_modifications.erase(mod_id)
	
	# 更新网格视觉
	update_grid_visual(mod_data, grid_pos, false)
	
	# 从玩家飞船移除视觉
	if visual_system:
		visual_system.unequip_modification_visual(mod_id)
	
	# 发送信号
	emit_signal("modification_unequipped", mod_id)
	
	print("NewModificationController: 已移除配件: " + mod_data.get("name", "未知"))

func update_grid_visual(mod_data: Dictionary, grid_pos: Vector2i, is_equipped: bool):
	"""更新网格视觉"""
	var shape_data = mod_data.get("shape_data", [])
	var color = mod_data.get("color", Color.WHITE)
	
	for offset in shape_data:
		var cell_pos = grid_pos + offset
		var cell_index = cell_pos.y * grid_size.x + cell_pos.x
		
		if cell_index >= 0 and cell_index < modification_grid.get_child_count():
			var cell = modification_grid.get_child(cell_index)
			var style = cell.get_theme_stylebox("panel").duplicate()
			
			if is_equipped:
				style.bg_color = color
				style.bg_color.a = 0.8
			else:
				# 恢复默认样式
				if cell_pos == grid_center:
					style.bg_color = Color(0.0, 0.5, 1.0, 0.7)
				else:
					style.bg_color = Color(0.2, 0.2, 0.3, 0.5)
			
			cell.add_theme_stylebox_override("panel", style)

func create_drag_preview(mod_data: Dictionary):
	"""创建拖拽预览"""
	clear_drag_preview()
	
	drag_preview = Panel.new()
	drag_preview.name = "DragPreview"
	drag_preview.mouse_filter = Control.MOUSE_FILTER_IGNORE
	
	# 设置预览样式
	var style = StyleBoxFlat.new()
	style.bg_color = mod_data.get("color", Color.WHITE)
	style.bg_color.a = 0.5
	style.border_width_left = 2
	style.border_width_top = 2
	style.border_width_right = 2
	style.border_width_bottom = 2
	style.border_color = Color.WHITE
	drag_preview.add_theme_stylebox_override("panel", style)
	
	# 设置大小
	var grid_size_data = mod_data.get("grid_size", Vector2i(1, 1))
	drag_preview.custom_minimum_size = Vector2(grid_size_data.x * 40, grid_size_data.y * 40)
	
	get_viewport().add_child(drag_preview)

func clear_drag_preview():
	"""清除拖拽预览"""
	if drag_preview:
		drag_preview.queue_free()
		drag_preview = null

func _input(event):
	"""处理输入事件"""
	if event.is_action_pressed("ui_cancel") and visible:
		_on_close_pressed()
		
	if drag_preview and event is InputEventMouseMotion:
		# 更新拖拽预览位置
		drag_preview.global_position = event.global_position - drag_preview.size / 2

func create_mod_part_from_dict(mod_dict: Dictionary) -> NewModificationData.ModificationPart:
	"""从字典创建配件对象"""
	return NewModificationData.ModificationPart.new(mod_dict)

func _on_visual_added(mod_id: String, visual_node: Node2D):
	"""处理视觉添加"""
	print("NewModificationController: 配件视觉已添加: " + mod_id)

func _on_visual_removed(mod_id: String):
	"""处理视觉移除"""
	print("NewModificationController: 配件视觉已移除: " + mod_id)

func get_equipped_modifications() -> Dictionary:
	"""获取已装备的配件"""
	return equipped_modifications

func clear_all_modifications():
	"""清除所有配件"""
	var mod_ids = equipped_modifications.keys()
	for mod_id in mod_ids:
		remove_modification(mod_id)
		inventory_system.add_modification_to_inventory(mod_id, 1)

func update_info_panel(mod_data: Dictionary):
	"""更新信息面板"""
	if not mod_name_label or not mod_desc_label or not mod_stats_label:
		return

	# 更新名称
	mod_name_label.text = mod_data.get("name", "未知配件")

	# 更新描述
	mod_desc_label.text = mod_data.get("description", "无描述")

	# 更新属性信息
	var stats_text = ""

	# 基础属性
	var health_bonus = mod_data.get("health_bonus", 0)
	var shield_bonus = mod_data.get("shield_bonus", 0)
	var damage_bonus = mod_data.get("damage_bonus", 0.0)
	var speed_bonus = mod_data.get("speed_bonus", 0.0)

	if health_bonus > 0:
		stats_text += "生命值 +" + str(health_bonus) + "\n"
	if shield_bonus > 0:
		stats_text += "护盾值 +" + str(shield_bonus) + "\n"
	if damage_bonus > 0:
		stats_text += "伤害 +" + str(damage_bonus * 100) + "%\n"
	if speed_bonus > 0:
		stats_text += "速度 +" + str(speed_bonus * 100) + "%\n"

	# 特殊效果
	var special_effects = mod_data.get("special_effects", [])
	if special_effects.size() > 0:
		stats_text += "\n特殊效果:\n"
		for effect in special_effects:
			stats_text += "• " + get_effect_description(effect) + "\n"

	# 尺寸和类型信息
	stats_text += "\n类型: " + get_type_display_name(mod_data.get("type", ""))
	stats_text += "\n尺寸: " + get_size_display_name(mod_data.get("size_category", ""))

	var grid_size = mod_data.get("grid_size", Vector2i(1, 1))
	stats_text += "\n占用: " + str(grid_size.x) + "x" + str(grid_size.y) + " 格"

	mod_stats_label.text = stats_text

func get_effect_description(effect: String) -> String:
	"""获取效果描述"""
	match effect:
		"pulse_shot":
			return "发射能量脉冲"
		"fractal_split":
			return "子弹分裂"
		"piercing_shot":
			return "穿透射击"
		"slow_fire_rate":
			return "射速降低"
		"physical_resistance":
			return "物理抗性"
		"energy_shield":
			return "能量护盾"
		"resonance_field":
			return "谐振力场"
		"force_field":
			return "力场防护"
		"enhanced_targeting":
			return "增强瞄准"
		"enemy_highlight":
			return "敌人高亮"
		"spawn_drones":
			return "生成无人机"
		"geometric_constructs":
			return "几何构造体"
		"gravity_field":
			return "引力场"
		"space_distortion":
			return "空间扭曲"
		"chaos_burst":
			return "混沌爆发"
		"unpredictable":
			return "不可预测效果"
		"energy_efficiency":
			return "能量效率提升"
		"mobius_effect":
			return "莫比乌斯效应"
		"extended_range":
			return "射程延长"
		"phase_penetration":
			return "相位穿透"
		"impact_resistance":
			return "冲击抗性"
		_:
			return effect

func get_type_display_name(type: String) -> String:
	"""获取类型显示名称"""
	match type:
		"core":
			return "核心型"
		"attack":
			return "攻击型"
		"defense":
			return "防御型"
		"special":
			return "特殊型"
		_:
			return "未知"

func get_size_display_name(size: String) -> String:
	"""获取尺寸显示名称"""
	match size:
		"small":
			return "小型"
		"medium":
			return "中型"
		"large":
			return "大型"
		_:
			return "未知"

func update_ship_stats():
	"""更新飞船状态显示"""
	if not health_value or not shield_value or not power_value or not mass_value:
		return

	# 计算总属性
	var total_health_bonus = 0
	var total_shield_bonus = 0
	var total_power_consumption = 0
	var total_mass = 0

	for mod_id in equipped_modifications.keys():
		var mod_data = equipped_modifications[mod_id]["data"]
		total_health_bonus += mod_data.get("health_bonus", 0)
		total_shield_bonus += mod_data.get("shield_bonus", 0)
		total_power_consumption += mod_data.get("power_consumption", 0)
		total_mass += mod_data.get("mass_value", 0)

	# 基础值
	var base_health = 10
	var base_shield = 5
	var base_power = 150
	var max_mass = 100

	# 更新显示
	health_value.text = str(base_health + total_health_bonus) + " / " + str(base_health + total_health_bonus)
	shield_value.text = str(base_shield + total_shield_bonus) + " / " + str(base_shield + total_shield_bonus)

	var current_power = base_power - total_power_consumption
	power_value.text = str(current_power) + " / " + str(base_power)

	mass_value.text = str(total_mass) + " / " + str(max_mass)

	# 根据状态设置颜色
	if current_power < 0:
		power_value.add_theme_color_override("font_color", Color.RED)
	else:
		power_value.add_theme_color_override("font_color", Color(1, 1, 0.5, 1))

	if total_mass > max_mass:
		mass_value.add_theme_color_override("font_color", Color.RED)
	else:
		mass_value.add_theme_color_override("font_color", Color(0.8, 0.8, 0.8, 1))

func show_modification_screen():
	"""显示改装界面"""
	show()
	get_tree().paused = true
	update_ship_stats()
