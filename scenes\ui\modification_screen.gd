extends Control

# 修改界面控制器
# 当玩家按下B键时显示/隐藏，包含5x5网格和底部物品栏

# 信号
signal modification_screen_opened
signal modification_screen_closed

# 界面组件引用
@onready var grid_panel = %GridPanel
@onready var grid_container = %GridContainer
@onready var player_placeholder = %PlayerPlaceholder
@onready var animation_container = $AnimationContainer

# 脚本引用
const ModInventoryItemScript = preload("res://scenes/ui/mod_inventory_item.gd")
const ModGridCellScript = preload("res://scenes/ui/mod_grid_cell.gd")

# 新物品栏引用
const ModInventoryPanelScene = preload("res://scenes/ui/mod_inventory_panel.tscn")
var mod_inventory_panel = null

# 常量定义
const PART_SCALE_FACTOR = 0.9
const PLAYER_SCALE_FACTOR = 0.7
const MODIFICATION_SCREEN_SCALE = 0.75  # 从0.85减小到0.75，进一步缩小界面

# 改装件数据
var placeholder_mods = [
	{"name": "测试配件1", "color": Color(0.3, 0.7, 1.0)},
	{"name": "测试配件2", "color": Color(0.3, 0.7, 1.0)},
	{"name": "测试配件3", "color": Color(0.3, 0.7, 1.0)},
	{"name": "测试配件4", "color": Color(0.3, 0.7, 1.0)},
	{"name": "测试配件5", "color": Color(0.3, 0.7, 1.0)}
]

# 占据的网格映射 - 用于追踪哪些格子已被占据
var occupied_cells = {}
# 玩家位置 - 中心位置
var player_position = Vector2(5, 4)  # 11x9网格的中心位置

# 界面状态
var is_open: bool = false
var is_animating: bool = false

# 网格单元格预设
const CELL_SIZE = Vector2(120, 120)  # 从100x100增加到120x120
const CELL_COUNT = 99  # 11x9
const GRID_SIZE = 11  # 列数

# 添加顶部预加载声明
const ModInventoryClass = preload("res://scenes/modifications/data/mod_inventory.gd")
# 移除视觉管理器相关引用
const ModificationResourceClass = preload("res://scenes/modifications/data/modification_resource.gd")

# 初始化
func _ready():
	# 初始状态为隐藏
	visible = false
	
	# 缩小到0大小（准备后续动画）
	grid_panel.scale = Vector2.ZERO
	
	# 应用整体缩放
	scale = Vector2.ONE * MODIFICATION_SCREEN_SCALE
	
	# 设置背景为半透明，让玩家能看到游戏情况
	$Background.scale = Vector2.ONE / MODIFICATION_SCREEN_SCALE  # 反向缩放以抵消父节点缩放
	$Background.anchor_right = 1.0 / MODIFICATION_SCREEN_SCALE
	$Background.anchor_bottom = 1.0 / MODIFICATION_SCREEN_SCALE
	$Background.mouse_filter = Control.MOUSE_FILTER_STOP
	$Background.color = Color(0, 0, 0, 0.3)  # 更透明的背景，让玩家能看到游戏
	
	# 初始化网格
	setup_grid()
	
	# 尝试加载已保存的改装状态
	load_modification_state()
	
	# 确保根节点可以接收拖放
	mouse_filter = Control.MOUSE_FILTER_STOP
	
	# 创建一个全屏的透明面板来捕获所有拖放事件
	var panel_bg = ColorRect.new()
	panel_bg.color = Color(0, 0, 0, 0.01)  # 几乎完全透明
	panel_bg.mouse_filter = Control.MOUSE_FILTER_PASS  # 确保鼠标事件传递
	panel_bg.anchors_preset = Control.PRESET_FULL_RECT
	panel_bg.z_index = -100  # 确保在最底层
	add_child(panel_bg)
	
	# 开启拖放处理（确保这个节点可以接收拖放）
	set_process_input(true)
	
	# 初始化新的物品栏
	_setup_new_inventory() 

# 设置新物品栏
func _setup_new_inventory():
	# 创建新的物品栏界面
	mod_inventory_panel = ModInventoryPanelScene.instantiate()
	add_child(mod_inventory_panel)
	
	# 调整物品栏的位置，向右下角移动
	var screen_size = get_viewport_rect().size
	mod_inventory_panel.size.y = 350 # 确保高度与panel设置一致
	
	# 设置锚点和偏移，确保位于屏幕底部
	mod_inventory_panel.anchors_preset = 15
	mod_inventory_panel.anchor_left = 0.0
	mod_inventory_panel.anchor_top = 1.0
	mod_inventory_panel.anchor_right = 1.0
	mod_inventory_panel.anchor_bottom = 1.0
	mod_inventory_panel.offset_left = 50.0
	mod_inventory_panel.offset_top = -350.0
	mod_inventory_panel.offset_right = -50.0
	mod_inventory_panel.grow_horizontal = 2
	mod_inventory_panel.grow_vertical = 0
	
	# 默认隐藏物品栏
	mod_inventory_panel.visible = false
	
	# 连接物品被选中的信号
	mod_inventory_panel.mod_item_selected.connect(_on_mod_item_selected)
	
	print("修改界面: 已初始化新的物品栏")

# 物品选中回调
func _on_mod_item_selected(mod_resource):
	print("修改界面: 已选中改装件 " + mod_resource.name)
	# 后续可以实现更多选中后的功能 

# 设置网格
func setup_grid():
	# 清空现有网格
	for child in grid_container.get_children():
		child.queue_free()
	
	# 清空占据格子映射
	occupied_cells.clear()
	
	# 创建11x9网格
	grid_container.columns = GRID_SIZE  # 设置为11列
	
	# 计算行数
	var row_count = 9  # 固定为9行
	
	# 更新玩家位置为网格中心
	player_position = Vector2(GRID_SIZE / 2, row_count / 2)
	if GRID_SIZE % 2 == 0:
		player_position.x -= 0.5
	if row_count % 2 == 0:
		player_position.y -= 0.5
		
	# 四舍五入确保位置是整数
	player_position = Vector2(round(player_position.x), round(player_position.y))
	
	print("玩家位置设置为: ", player_position)
	
	# 创建网格单元格
	for i in range(GRID_SIZE * row_count):
		var cell = Panel.new()
		cell.custom_minimum_size = CELL_SIZE
		cell.size_flags_horizontal = Control.SIZE_FILL
		cell.size_flags_vertical = Control.SIZE_FILL
		
		# 计算行列位置（用于后续处理）
		var row = i / GRID_SIZE
		var col = i % GRID_SIZE
		
		# 应用网格单元格脚本
		cell.set_script(ModGridCellScript)
		cell.grid_position = Vector2(col, row)
		
		# 保存对修改界面的引用，以便调用连通性检查
		cell.modification_screen = self
		
		# 如果是中心单元格，标记为玩家位置
		if Vector2(col, row) == player_position:
			# 创建更明显的玩家显示
			cell.modulate = Color(0.2, 0.8, 0.2, 0.8)  # 绿色半透明背景

			# 添加玩家图标
			var player_icon = ColorRect.new()
			player_icon.color = Color(1.0, 1.0, 1.0, 0.9)  # 白色玩家图标
			player_icon.size = Vector2(80, 80)  # 较大的图标
			player_icon.position = Vector2(20, 20)  # 居中
			player_icon.mouse_filter = Control.MOUSE_FILTER_IGNORE
			cell.add_child(player_icon)

			# 添加边框
			var border = ReferenceRect.new()
			border.border_color = Color(1.0, 1.0, 0.0, 1.0)  # 黄色边框
			border.border_width = 3
			border.size = CELL_SIZE
			border.mouse_filter = Control.MOUSE_FILTER_IGNORE
			cell.add_child(border)

			# 暂时禁用玩家位置的拖放功能
			cell.mouse_filter = Control.MOUSE_FILTER_IGNORE
			# 标记中心为玩家位置 - 默认已占据
			occupied_cells[Vector2(col, row)] = {"is_player": true}
		
		grid_container.add_child(cell)

# 从全局状态加载改装状态
func load_modification_state():
	# 示例实现
	print("加载改装状态")
	
	# 在实际的游戏中，这里可以从全局状态或保存文件加载改装状态
	# 例如：
	# var game_state = get_tree().get_first_node_in_group("game_state")
	# if game_state and game_state.has_method("load_modification_state"):
	#     var state = game_state.load_modification_state()
	#     if state:
	#         # 恢复状态
	#         occupied_cells = state

# 保存当前改装状态到全局状态
func save_modification_state():
	# 示例实现
	print("保存改装状态")
	
	# 在实际的游戏中，这里可以将状态保存到全局状态或保存文件
	# 例如：
	# var game_state = get_tree().get_first_node_in_group("game_state")
	# if game_state and game_state.has_method("save_modification_state"):
	#     game_state.save_modification_state(occupied_cells)

# 判断是否可以在指定位置放置改装件
func can_place_at_position(position: Vector2) -> bool:
	# 如果是玩家位置，不能放置
	if position == player_position:
		return false
	
	# 检查是否已经被占据
	if occupied_cells.has(position):
		return false
	
	# 检查是否与玩家直接相邻
	if is_adjacent_to_player(position):
		return true
	
	# 检查是否通过其他改装件与玩家相连
	return is_connected_to_player(position)

# 检查是否与玩家直接相邻
func is_adjacent_to_player(position: Vector2) -> bool:
	# 上下左右四个方向
	var directions = [
		Vector2(0, -1),  # 上
		Vector2(1, 0),   # 右
		Vector2(0, 1),   # 下
		Vector2(-1, 0)   # 左
	]
	
	# 检查四个方向
	for dir in directions:
		var neighbor = position + dir
		if neighbor == player_position:
			return true
	
	return false

# 检查是否通过其他改装件与玩家相连
func is_connected_to_player(position: Vector2) -> bool:
	# 广度优先搜索找到是否有路径连接到玩家
	var visited = {}
	var queue = []
	
	# 添加相邻的已占据格子作为起点
	var directions = [
		Vector2(0, -1),  # 上
		Vector2(1, 0),   # 右
		Vector2(0, 1),   # 下
		Vector2(-1, 0)   # 左
	]
	
	# 首先检查是否直接与玩家相邻
	for dir in directions:
		var neighbor = position + dir
		if neighbor == player_position:
			return true
	
	# 然后检查是否通过其他模块连接到玩家
	for dir in directions:
		var neighbor = position + dir
		if occupied_cells.has(neighbor):
			queue.append(neighbor)
			visited[neighbor] = true
	
	# 如果没有相邻的已占据格子，直接返回false
	if queue.size() == 0:
		return false
	
	# 广度优先搜索
	while queue.size() > 0:
		var current = queue.pop_front()
		
		# 如果找到玩家，返回true
		if current == player_position:
			return true
		
		# 检查相邻格子
		for dir in directions:
			var neighbor = current + dir
			
			# 如果已访问过或不在占据列表中，跳过
			if visited.has(neighbor) or not occupied_cells.has(neighbor):
				continue
			
			# 标记为已访问并添加到队列
			visited[neighbor] = true
			queue.append(neighbor)
	
	# 没有找到路径到玩家
	return false

# 修改mark_cell_occupied函数，移除视觉反馈
func mark_cell_occupied(position: Vector2, data):
	# 记录占据状态
	occupied_cells[position] = data

# 修改clear_cell_occupied函数，移除视觉反馈
func clear_cell_occupied(position: Vector2):
	# 从占据状态中移除
	if occupied_cells.has(position):
		occupied_cells.erase(position)

# 更新网格可放置状态视觉显示
func update_grid_placement_visuals():
	# 遍历所有网格单元格
	for i in range(grid_container.get_child_count()):
		var cell = grid_container.get_child(i)
		if cell.has_method("update_placement_status"):
			cell.update_placement_status()

# 切换修改界面显示状态
func toggle():
	if is_animating:
		return
		
	if is_open:
		close()
	else:
		open()

# 打开修改界面
func open():
	if is_open or is_animating:
		return
		
	is_animating = true
	
	# 显示界面
	visible = true
	
	# 设置现有网格格子的状态
	update_grid_cells_from_occupied()
	
	# 更新网格可放置状态视觉提示
	update_grid_placement_visuals()
	
	# 显示新的物品栏并调整位置
	mod_inventory_panel.visible = true

	# 获取视口尺寸用于位置计算
	var viewport_size = get_viewport_rect().size

	# 强制设置改装仓库位置到屏幕底部
	# 使用 call_deferred 确保在下一帧设置，避免被其他代码覆盖
	call_deferred("_force_inventory_position")

	print("改装仓库位置设置: 底部, 视口尺寸: ", viewport_size)
	
	# 进入慢动作模式而不是暂停游戏
	Engine.time_scale = 0.1  # 降低游戏速度到10%
	
	# 开始打开动画 - 补偿慢动作影响
	var time_compensation = 1.0 / Engine.time_scale  # 补偿因子
	var initial_delay = 0.3 / time_compensation  # 初始显示延迟
	var scale_duration = 0.6 / time_compensation  # 缩放动画时间 - 加快0.2秒
	
	# 确保背景覆盖整个屏幕
	$Background.size = viewport_size * (1.0 / MODIFICATION_SCREEN_SCALE)
	$Background.position = Vector2.ZERO
	
	# 确保整个界面在屏幕中央，向右上方调整
	animation_container.position = Vector2(
		viewport_size.x * (1 - MODIFICATION_SCREEN_SCALE) / 2 + 30,  # 向右偏移30像素
		viewport_size.y * (1 - MODIFICATION_SCREEN_SCALE) / 2 - 60   # 向上偏移60像素
	)
	
	# 确保网格面板位于屏幕正中央，略微向右上角偏移以补偿缩放效果
	var grid_size = grid_panel.size
	grid_panel.position = Vector2(
		viewport_size.x / 2 - grid_size.x / 2 + 70,  # 向右偏移70像素
		viewport_size.y / 2 - grid_size.y / 2 - 70   # 向上偏移70像素
	)
	
	# 确保网格面板的中心点是从中心开始扩展的
	grid_panel.pivot_offset = grid_panel.size/2
	
	# 设置初始缩放 - 从一个很小的点开始
	grid_panel.scale = Vector2(0.05, 0.05)  # 非常小的初始大小
	
	# 创建序列补间
	var sequence = create_tween()
	
	# 先停顿短暂时间
	sequence.tween_interval(initial_delay)
	
	# 网格从中心点向四周扩展 - 加快速度
	sequence.tween_property(grid_panel, "scale", Vector2(1, 1), scale_duration).set_ease(Tween.EASE_OUT).set_trans(Tween.TRANS_BACK)
	
	# 动画完成后更新状态
	sequence.tween_callback(func():
		is_animating = false
		is_open = true
		emit_signal("modification_screen_opened")
	)

# 从已占据的格子数据更新网格格子显示
func update_grid_cells_from_occupied():
	# 清空所有现有网格内容
	for i in range(grid_container.get_child_count()):
		var cell = grid_container.get_child(i)
		if cell.has_method("clear_modification_silent"):
			cell.clear_modification_silent()
	
	# 根据占据状态重新填充网格
	for pos in occupied_cells.keys():
		# 跳过玩家位置
		if occupied_cells[pos].has("is_player") and occupied_cells[pos]["is_player"]:
			continue
		
		# 从已保存数据中恢复改装件显示
		if occupied_cells[pos].has("name") and occupied_cells[pos].has("color"):
			# 找到对应的网格格子
			for i in range(grid_container.get_child_count()):
				var cell = grid_container.get_child(i)
				if cell.grid_position == pos:
					# 放置改装件
					cell.place_modification(occupied_cells[pos]["name"], occupied_cells[pos]["color"])
					break 

# 关闭修改界面
func close():
	if !is_open or is_animating:
		return
		
	is_animating = true
	
	# 保存当前改装状态到全局状态（如果需要）
	save_modification_state()
	
	# 隐藏新的物品栏
	mod_inventory_panel.visible = false
	
	# 获取视口尺寸
	var viewport_size = get_viewport_rect().size
	
	# 计算动画时间补偿
	var time_compensation = 1.0 / Engine.time_scale  # 补偿因子
	var animation_duration = 0.55 / time_compensation   # 动画时间 - 减慢0.2秒
	
	# 获取玩家位置 - 改进定位方式
	var player_node = get_tree().get_first_node_in_group("player")
	var target_pos = grid_panel.position  # 默认不移动

	if player_node:
		# 获取玩家的全局变换
		var canvas = get_canvas_transform()
		var global_pos = player_node.global_position
		
		# 转换为屏幕坐标并调整偏移，确保准确定位到玩家中心
		target_pos = global_pos
		
		# 打印调试信息，帮助排查位置问题
		print("玩家位置: ", global_pos)
		print("目标位置: ", target_pos)
		
		# 设置缩小后的大小以匹配玩家大小
		var player_size = 40  # 估计玩家尺寸，从30增加到40
		var final_scale = player_size / grid_panel.size.x
		
		# 创建并行补间 - 同时缩小和移动
		var tween = create_tween().set_parallel()
		
		# 先移动面板的枢轴点到左上角，以便能正确缩放到玩家位置
		grid_panel.pivot_offset = Vector2.ZERO
		
		# 调整目标位置，考虑面板大小
		var offset_x = grid_panel.size.x * final_scale / 2
		var offset_y = grid_panel.size.y * final_scale / 2
		target_pos = target_pos - Vector2(offset_x, offset_y)
		
		# 同时缩小并移动到玩家位置 - 减慢速度
		tween.tween_property(grid_panel, "scale", Vector2(final_scale, final_scale), animation_duration).set_ease(Tween.EASE_IN).set_trans(Tween.TRANS_BACK)
		tween.tween_property(grid_panel, "position", target_pos, animation_duration).set_ease(Tween.EASE_IN).set_trans(Tween.TRANS_QUINT)
		
		# 动画完成后更新状态
		tween.chain().tween_callback(func():
			is_animating = false
			is_open = false
			visible = false
			
			# 恢复正常游戏速度
			Engine.time_scale = 1.0
			
			# 确保发送信号，让玩家更新改装状态
			emit_signal("modification_screen_closed")
		)
	else:
		# 如果找不到玩家，仍然执行关闭动画
		var tween = create_tween().set_parallel()
		
		# 简单缩小到中心点 - 减慢速度
		tween.tween_property(grid_panel, "scale", Vector2(0.05, 0.05), animation_duration).set_ease(Tween.EASE_IN).set_trans(Tween.TRANS_BACK)
		
		# 动画完成后更新状态
		tween.chain().tween_callback(func():
			is_animating = false
			is_open = false
			visible = false
			
			# 恢复正常游戏速度
			Engine.time_scale = 1.0
			
			# 确保发送信号，让玩家更新改装状态
			emit_signal("modification_screen_closed")
		)

# 获取当前装备的改装件
func get_equipped_modifications():
	# 返回当前占据的格子信息，但将Vector2键转换为字符串格式
	var result = {}
	for pos_key in occupied_cells.keys():
		# 将Vector2键转换为字符串格式
		var str_key = str(pos_key.x) + "," + str(pos_key.y)
		result[str_key] = occupied_cells[pos_key]
	return result

# 处理拖拽操作，检查连接断开的情况
func handle_mod_drag(from_position: Vector2, keep_dragged_mod: bool = false) -> bool:
	# 获取如果移除这个位置会导致哪些配件失去连接
	var disconnected_positions = get_disconnected_mods_if_removed(from_position)

	# 如果需要保留被拖拽的道具，确保from_position不在disconnected_positions中
	if keep_dragged_mod and disconnected_positions.has(from_position):
		disconnected_positions.erase(from_position)

	# 保存当前被拖拽道具的信息，以便后续添加到物品栏
	var dragged_mod_data = null
	if occupied_cells.has(from_position):
		dragged_mod_data = occupied_cells[from_position].duplicate()
		dragged_mod_data["position"] = from_position

	if disconnected_positions.size() > 0:
		print("拖走此配件会导致 " + str(disconnected_positions.size()) + " 个配件失去连接")

		# 收集失去连接的改装配件
		var collected_mods = collect_disconnected_mods(disconnected_positions)

		# 将这些改装配件添加回物品栏
		add_mods_to_inventory(collected_mods)

	# 无论是否有断开连接的配件，都清除被拖拽的配件
	if keep_dragged_mod and dragged_mod_data != null:
		# 找到并清除被拖拽格子的内容
		for i in range(grid_container.get_child_count()):
			var cell = grid_container.get_child(i)
			if cell.grid_position == from_position:
				cell.clear_modification_silent()
				# 清除占据状态
				clear_cell_occupied(from_position)
				print("清除格子 " + str(from_position) + " 的配件: [" + dragged_mod_data["name"] + "]")
				break

	return disconnected_positions.size() > 0

# 检查如果移除一个位置的配件，会导致哪些配件失去连接
func get_disconnected_mods_if_removed(position: Vector2) -> Array:
	# 保存当前占据状态的副本
	var original_occupied = occupied_cells.duplicate(true)
	var disconnected_positions = []

	# 暂时移除这个位置
	if occupied_cells.has(position):
		occupied_cells.erase(position)

	# 检查所有当前占据的非玩家格子
	for pos in occupied_cells.keys():
		# 跳过玩家位置
		if occupied_cells[pos].has("is_player") and occupied_cells[pos]["is_player"]:
			continue

		# 检查是否仍然连接到玩家
		if not is_connected_to_player_from_position(pos):
			disconnected_positions.append(pos)

	# 恢复原始占据状态
	occupied_cells = original_occupied

	return disconnected_positions

# 收集失去连接的改装配件并返回到物品栏
func collect_disconnected_mods(disconnected_positions: Array) -> Array:
	var collected_mods = []

	# 收集所有失去连接的改装配件信息
	for pos in disconnected_positions:
		if occupied_cells.has(pos):
			var mod_data = occupied_cells[pos]
			collected_mods.append({
				"name": mod_data["name"],
				"color": mod_data["color"],
				"position": pos
			})

			# 从占据状态中移除
			occupied_cells.erase(pos)

	# 找到所有网格单元格并清除这些位置的改装配件
	for i in range(grid_container.get_child_count()):
		var cell = grid_container.get_child(i)
		for pos in disconnected_positions:
			if cell.grid_position == pos:
				cell.clear_modification_silent()  # 不更新全局占据状态

	# 更新网格可放置状态
	update_grid_placement_visuals()

	return collected_mods

# 检查指定位置是否通过其他改装件连接到玩家
func is_connected_to_player_from_position(start_position: Vector2) -> bool:
	# 如果起始位置就是玩家位置，直接返回true
	if start_position == player_position:
		return true

	# 如果起始位置与玩家直接相邻，返回true
	if is_adjacent_to_player(start_position):
		return true

	# 使用广度优先搜索检查连接性
	var visited = {}
	var queue = [start_position]
	visited[start_position] = true

	# 四个方向
	var directions = [Vector2(0, 1), Vector2(0, -1), Vector2(1, 0), Vector2(-1, 0)]

	# 广度优先搜索
	while queue.size() > 0:
		var current = queue.pop_front()

		# 如果找到玩家，返回true
		if current == player_position:
			return true

		# 检查相邻格子
		for dir in directions:
			var neighbor = current + dir

			# 如果已访问过或不在占据列表中，跳过
			if visited.has(neighbor) or not occupied_cells.has(neighbor):
				continue

			# 标记为已访问并添加到队列
			visited[neighbor] = true
			queue.append(neighbor)

	# 没有找到路径到玩家
	return false

# 将改装配件添加回物品栏
func add_mods_to_inventory(mods: Array):
	for mod_data in mods:
		# 使用新的物品栏系统添加道具
		add_to_inventory_list(mod_data["name"], mod_data["color"])
		print("已将道具 [" + mod_data["name"] + "] 添加回改装仓库")

# 强制设置改装仓库位置
func _force_inventory_position():
	if not mod_inventory_panel:
		return

	# 获取当前视口尺寸
	var viewport_size = get_viewport_rect().size

	# 强制设置锚点和位置
	mod_inventory_panel.set_anchors_preset(Control.PRESET_BOTTOM_WIDE)
	mod_inventory_panel.anchor_left = 0.0
	mod_inventory_panel.anchor_top = 1.0
	mod_inventory_panel.anchor_right = 1.0
	mod_inventory_panel.anchor_bottom = 1.0

	# 设置偏移量
	mod_inventory_panel.offset_left = 50.0
	mod_inventory_panel.offset_top = -350.0
	mod_inventory_panel.offset_right = -50.0
	mod_inventory_panel.offset_bottom = 0.0

	# 设置尺寸
	mod_inventory_panel.size = Vector2(viewport_size.x - 100, 350)

	# 确保位置正确
	mod_inventory_panel.position = Vector2(50, viewport_size.y - 350)

	print("强制设置改装仓库位置: ", mod_inventory_panel.position, " 尺寸: ", mod_inventory_panel.size)

# 从物品栏列表中移除道具
func remove_from_inventory_list(mod_name: String):
	if not mod_inventory_panel or not mod_inventory_panel.mod_inventory_ui:
		return

	# 通知改装仓库UI移除道具
	if mod_inventory_panel.mod_inventory_ui.has_method("remove_modification_from_display"):
		mod_inventory_panel.mod_inventory_ui.remove_modification_from_display(mod_name)
		print("已从改装仓库显示中移除: " + mod_name)

# 添加道具回物品栏列表
func add_to_inventory_list(mod_name: String, mod_color: Color):
	if not mod_inventory_panel or not mod_inventory_panel.mod_inventory_ui:
		return

	# 通知改装仓库UI添加道具
	if mod_inventory_panel.mod_inventory_ui.has_method("add_modification_to_display"):
		mod_inventory_panel.mod_inventory_ui.add_modification_to_display(mod_name, mod_color)
		print("已添加到改装仓库显示: " + mod_name)

# 获取界面开启状态
func is_modification_screen_open() -> bool:
	return is_open

# 处理从物品栏拖拽的操作
func handle_inventory_drag(drag_data):
	print("处理物品栏拖拽操作: ", drag_data)

	if drag_data.has("type") and drag_data["type"] == "modification_part_placeholder":
		# 如果是从物品栏拖拽的配件
		if drag_data.has("source_type") and drag_data["source_type"] == "inventory":
			# 从物品栏移除该物品
			if drag_data.has("source_item"):
				drag_data["source_item"].remove_from_inventory()
				print("已从物品栏移除拖拽的配件")
		elif drag_data.has("source_type") and drag_data["source_type"] == "new_inventory":
			# 从新物品栏移除该物品
			if mod_inventory_panel and mod_inventory_panel.has_method("remove_modification_from_display"):
				mod_inventory_panel.remove_modification_from_display(drag_data["name"])
				print("已从新物品栏移除拖拽的配件")

	return true

# 处理拖拽到空白区域的情况
func _can_drop_data(position: Vector2, data) -> bool:
	# 如果界面没有打开，不接受拖拽
	if not is_open:
		return false

	# 检查是否是改装配件
	if data.has("type") and data["type"] == "modification_part_placeholder":
		return true

	return false

# 处理拖拽数据
func _drop_data(position: Vector2, data):
	print("拖拽到空白区域，将配件返回物品栏")

	# 将配件添加回物品栏
	if data.has("name") and data.has("color"):
		add_mods_to_inventory([{"name": data["name"], "color": data["color"]}])
