extends Node

class_name ModDataManagerLegacy

# 改装数据管理器 - 旧版
# 负责管理所有改装配件的数据

# 单例实例
static var _instance = null

# 改装配件数据字典
var modifications_data = {}

# 获取单例实例
static func get_instance():
	if _instance == null:
		_instance = ModDataManagerLegacy.new()
	return _instance

# 初始化
func _init():
	# 加载默认改装配件数据
	_load_default_modifications()

# 加载默认改装配件数据
func _load_default_modifications():
	# 创建一些默认的改装配件数据
	# 这里只是示例，实际项目中应该从配置文件或数据库加载
	
	# 护盾增强器
	var shield_mod = ModificationResource.new()
	shield_mod.id = "shield_enhancer"
	shield_mod.name = "护盾增强器"
	shield_mod.description = "增加1点护盾上限"
	shield_mod.color = Color(0.3, 0.7, 1.0)  # 蓝色
	shield_mod.shield_bonus = 1
	shield_mod.add_effect(ModificationEffect.create(ModificationEffect.EffectType.SHIELD_BONUS, 1))
	modifications_data[shield_mod.id] = shield_mod
	
	# 生命增强器
	var health_mod = ModificationResource.new()
	health_mod.id = "health_enhancer"
	health_mod.name = "生命增强器"
	health_mod.description = "增加1点生命上限"
	health_mod.color = Color(1.0, 0.3, 0.3)  # 红色
	health_mod.health_bonus = 1
	modifications_data[health_mod.id] = health_mod
	
	# 速度增强器
	var speed_mod = ModificationResource.new()
	speed_mod.id = "speed_enhancer"
	speed_mod.name = "速度增强器"
	speed_mod.description = "增加10%移动速度"
	speed_mod.color = Color(0.3, 1.0, 0.3)  # 绿色
	speed_mod.speed_bonus = 0.1
	modifications_data[speed_mod.id] = speed_mod
	
	# 伤害增强器
	var damage_mod = ModificationResource.new()
	damage_mod.id = "damage_enhancer"
	damage_mod.name = "伤害增强器"
	damage_mod.description = "增加15%伤害"
	damage_mod.color = Color(1.0, 0.7, 0.2)  # 橙色
	damage_mod.damage_bonus = 0.15
	modifications_data[damage_mod.id] = damage_mod
	
	print("ModDataManagerLegacy: 已加载 " + str(modifications_data.size()) + " 个默认改装配件")

# 获取改装配件数据
func get_modification_data(id: String) -> ModificationResource:
	if modifications_data.has(id):
		return modifications_data[id]
	
	# 如果找不到，返回一个默认配件
	print("ModDataManagerLegacy: 警告 - 找不到ID为 " + id + " 的改装配件，返回默认配件")
	var default_mod = ModificationResource.new()
	default_mod.id = id
	default_mod.name = "未知配件"
	default_mod.description = "未知配件"
	default_mod.color = Color(0.5, 0.5, 0.5)  # 灰色
	return default_mod

# 添加改装配件数据
func add_modification_data(mod_resource: ModificationResource) -> void:
	modifications_data[mod_resource.id] = mod_resource
	print("ModDataManagerLegacy: 已添加改装配件 - " + mod_resource.name)

# 获取所有改装配件ID列表
func get_all_modification_ids() -> Array:
	return modifications_data.keys() 
