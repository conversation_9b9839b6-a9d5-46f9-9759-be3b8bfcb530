extends Node

# 改装配件系统测试脚本
# 用于测试改装配件系统的功能

# 玩家引用
var player_node = null
var mod_system = null
var mod_manager = null

# 测试用配件
var test_mods = ["测试配件1", "测试配件2", "测试配件3", "测试配件4", "测试配件5"]
var test_positions = [
	"1,2",  # 左
	"3,2",  # 右
	"2,1",  # 上
	"2,3",  # 下
	"1,1"   # 左上
]

func _ready():
	# 延迟初始化，确保其他节点已加载
	await get_tree().create_timer(1.0).timeout
	initialize()

# 初始化
func initialize():
	print("\n===== 开始改装配件系统测试 =====")
	
	# 查找玩家节点
	player_node = get_tree().get_first_node_in_group("player")
	if not player_node:
		push_error("测试脚本: 无法找到玩家节点!")
		return
	
	print("找到玩家节点: " + player_node.name)
	
	# 查找改装系统
	mod_system = player_node.get_node_or_null("ModificationSystem")
	if not mod_system:
		push_error("测试脚本: 无法找到改装系统!")
		return
	
	print("找到改装系统: " + mod_system.name)
	
	# 查找改装管理器
	mod_manager = player_node.get_node_or_null("ModificationManager")
	if not mod_manager:
		push_error("测试脚本: 无法找到改装管理器!")
		return
	
	print("找到改装管理器: " + mod_manager.name)
	
	# 开始测试
	await get_tree().create_timer(1.0).timeout
	run_tests()

# 运行测试
func run_tests():
	print("\n----- 开始测试新的改装配件系统 -----")
	
	# 初始化护盾值
	print("\n初始化玩家护盾")
	player_node.max_shield_points = GameConstants.PLAYER_MAX_SHIELD
	player_node.shield_points = 1
	print("初始护盾值设置为: 1/" + str(GameConstants.PLAYER_MAX_SHIELD))
	
	# 打印初始状态
	print_player_stats()
	
	await get_tree().create_timer(1.0).timeout
	
	# 测试1: 模拟关闭装备栏 - 应用配件
	print("\n测试1: 模拟关闭装备栏 - 应用配件")
	
	# 创建测试配件集
	var test_mod_set = {}
	for i in range(test_mods.size()):
		var pos_str = test_positions[i]  # 直接使用字符串位置
		test_mod_set[pos_str] = {
			"name": test_mods[i],
			"color": Color(0.3, 0.7, 1.0)
		}
	
	# 添加玩家位置
	test_mod_set["2,2"] = {
		"name": "Player",
		"color": Color.WHITE,
		"is_player": true
	}
	
	print("模拟关闭装备栏，应用配件集: " + str(test_mod_set.size() - 1) + " 个配件")
	
	# 直接调用关闭装备栏的回调
	await mod_system._on_modification_screen_closed_test(test_mod_set)
	
	# 打印当前状态
	print_player_stats()
	
	# 等待一段时间
	await get_tree().create_timer(2.0).timeout
	
	# 测试2: 模拟关闭装备栏 - 清除配件
	print("\n测试2: 模拟关闭装备栏 - 清除配件")
	
	# 创建空的配件集，只保留玩家
	var empty_mod_set = {
		"2,2": {
			"name": "Player",
			"color": Color.WHITE,
			"is_player": true
		}
	}
	
	print("模拟关闭装备栏，清除所有配件")
	
	# 直接调用关闭装备栏的回调
	await mod_system._on_modification_screen_closed_test(empty_mod_set)
	
	# 打印当前状态
	print_player_stats()
	
	# 等待一段时间
	await get_tree().create_timer(2.0).timeout
	
	# 测试3: 测试新的效果接口
	print("\n测试3: 测试新的效果接口")
	
	# 创建和应用一个护盾效果
	var shield_effect = ModificationEffect.create(
		ModificationEffect.EffectType.SHIELD_BONUS,
		2.0,
		"test_shield_effect",
		"测试护盾加成效果"
	)
	
	print("直接应用护盾效果: +2")
	shield_effect.apply(player_node)
	
	# 打印当前状态
	print_player_stats()
	
	# 等待一段时间
	await get_tree().create_timer(2.0).timeout
	
	# 重置玩家属性
	print("\n重置玩家属性")
	mod_system.reset_player_stats()
	
	# 打印当前状态
	print_player_stats()
	
	print("\n----- 改装配件测试完成 -----")
	
	# 测试4: 测试改装配件视觉效果和碰撞箱
	print("\n测试4: 测试改装配件视觉效果和碰撞箱")
	
	# 检查视觉管理器
	var visuals_manager = player_node.get_node_or_null("ModificationVisualsManager")
	if not visuals_manager:
		print("视觉管理器不存在，跳过测试")
		return
	
	print("找到视觉管理器: " + visuals_manager.name)
	
	# 测试添加一个配件
	print("\n添加一个左侧配件，测试视觉效果和碰撞箱")
	
	# 创建仅含一个左侧配件的集合
	var left_mod_set = {
		"1,2": {  # 左侧位置
			"name": test_mods[0],
			"color": Color(0.3, 0.7, 1.0)  # 蓝色
		},
		"2,2": {  # 玩家位置
			"name": "Player",
			"color": Color.WHITE,
			"is_player": true
		}
	}
	
	# 记录原始碰撞箱大小
	var original_collision_size = visuals_manager.get_collision_size()
	print("原始碰撞箱大小: " + str(original_collision_size))
	
	# 应用改装配件
	await mod_system._on_modification_screen_closed_test(left_mod_set)
	
	# 等待一段时间
	await get_tree().create_timer(1.0).timeout
	
	# 检查碰撞箱大小
	var new_collision_size = visuals_manager.get_collision_size()
	print("添加左侧配件后碰撞箱大小: " + str(new_collision_size))
	
	# 测试移除配件
	print("\n移除所有配件，测试碰撞箱恢复")
	
	# 创建空的配件集
	var empty_mod_set = {
		"2,2": {  # 玩家位置
			"name": "Player",
			"color": Color.WHITE,
			"is_player": true
		}
	}
	
	# 应用空配件集
	await mod_system._on_modification_screen_closed_test(empty_mod_set)
	
	# 等待一段时间
	await get_tree().create_timer(1.0).timeout
	
	# 检查碰撞箱大小
	var reset_collision_size = visuals_manager.get_collision_size()
	print("移除所有配件后碰撞箱大小: " + str(reset_collision_size))
	
	print("\n----- 改装配件视觉效果和碰撞箱测试完成 -----")

# 打印玩家状态
func print_player_stats():
	if player_node:
		print("玩家状态:")
		print("- 护盾: " + str(player_node.shield_points) + "/" + str(player_node.max_shield_points))
		print("- 生命: " + str(player_node.hp) + "/" + str(player_node.max_hp))
		print("- 移动速度: " + str(player_node.move_speed))
	else:
		print("无法获取玩家状态")

# 清理函数
func _exit_tree():
	print("改装配件测试脚本已卸载") 