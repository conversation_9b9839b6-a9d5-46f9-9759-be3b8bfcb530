extends Node

class_name ModInventory

# 添加必要的类引用
const ModificationDataManager = preload("res://scenes/modifications/data/modification_data_manager.gd")

# 单例实例
static var _instance = null

# 库存数据结构：{mod_id: {quantity: 数量, level: 等级}}
var inventory = {}

# 获取单例实例
static func get_instance():
	if _instance == null:
		_instance = ModInventory.new()
	return _instance

# 初始化
func _init():
	# 添加一些测试改装件
	_add_test_modifications()

# 添加测试改装件
func _add_test_modifications():
	# 添加一些默认改装件到库存
	add_modification("shield_enhancer", 3, 1)  # 3个1级护盾增强器
	add_modification("health_enhancer", 2, 1)  # 2个1级生命增强器
	add_modification("speed_enhancer", 1, 2)   # 1个2级速度增强器
	add_modification("damage_enhancer", 2, 1)  # 2个1级伤害增强器
	
	print("ModInventory: 已添加测试改装件到库存")

# 添加改装件到库存
func add_modification(mod_id: String, quantity: int = 1, level: int = 1) -> void:
	if inventory.has(mod_id):
		# 已存在，增加数量
		inventory[mod_id]["quantity"] += quantity
	else:
		# 不存在，创建新条目
		inventory[mod_id] = {
			"quantity": quantity,
			"level": level
		}
	
	print("ModInventory: 已添加 " + str(quantity) + " 个 " + mod_id + " (等级: " + str(level) + ")")

# 移除改装件
func remove_modification(mod_id: String, quantity: int = 1) -> bool:
	if not inventory.has(mod_id):
		print("ModInventory: 尝试移除不存在的改装件 " + mod_id)
		return false
	
	if inventory[mod_id]["quantity"] < quantity:
		print("ModInventory: 改装件 " + mod_id + " 数量不足，当前: " + str(inventory[mod_id]["quantity"]) + ", 请求: " + str(quantity))
		return false
	
	# 减少数量
	inventory[mod_id]["quantity"] -= quantity
	
	# 如果数量为0，移除条目
	if inventory[mod_id]["quantity"] <= 0:
		inventory.erase(mod_id)
	
	print("ModInventory: 已移除 " + str(quantity) + " 个 " + mod_id)
	return true

# 获取改装件数量
func get_modification_quantity(mod_id: String) -> int:
	if inventory.has(mod_id):
		return inventory[mod_id]["quantity"]
	return 0

# 获取改装件等级
func get_modification_level(mod_id: String) -> int:
	if inventory.has(mod_id):
		return inventory[mod_id]["level"]
	return 0

# 升级改装件
func upgrade_modification(mod_id: String) -> bool:
	if not inventory.has(mod_id):
		print("ModInventory: 尝试升级不存在的改装件 " + mod_id)
		return false
	
	# 增加等级
	inventory[mod_id]["level"] += 1
	
	print("ModInventory: 已升级 " + mod_id + " 到等级 " + str(inventory[mod_id]["level"]))
	return true

# 获取所有库存中的改装件ID
func get_all_modification_ids() -> Array:
	return inventory.keys()

# 获取库存中特定类型的改装件ID
func get_modifications_by_type(type_tag: String) -> Array:
	var result = []
	var mod_data = ModificationDataManager.get_instance()
	
	for mod_id in inventory.keys():
		var mod = mod_data.get_modification_data(mod_id)
		if mod and mod.has_effect_tag(type_tag):
			result.append(mod_id)
	
	return result

# 获取库存中特定尺寸的改装件ID
func get_modifications_by_size(size_tag: String) -> Array:
	var result = []
	var mod_data = ModificationDataManager.get_instance()
	
	for mod_id in inventory.keys():
		var mod = mod_data.get_modification_data(mod_id)
		if mod and mod.has_effect_tag(size_tag):
			result.append(mod_id)
	
	return result

# 获取完整库存数据（用于存档）
func get_inventory_data() -> Dictionary:
	return inventory.duplicate(true)

# 设置完整库存数据（用于读档）
func set_inventory_data(data: Dictionary) -> void:
	inventory = data.duplicate(true)
	print("ModInventory: 已从存档加载库存数据，共 " + str(inventory.size()) + " 种改装件") 