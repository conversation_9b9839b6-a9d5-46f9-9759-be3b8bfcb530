extends Control
class_name NewModInventorySystem

# 新的改装仓库系统
# 完全独立的实现，不依赖旧系统

signal mod_selected(mod_data)
signal inventory_closed

# UI组件
@onready var background_panel = $BackgroundPanel
@onready var title_label = $BackgroundPanel/MainContainer/HeaderContainer/TitleLabel
@onready var close_button = $BackgroundPanel/MainContainer/HeaderContainer/CloseButton

# 筛选器
@onready var size_filter_container = $BackgroundPanel/MainContainer/FiltersContainer/SizeFilters
@onready var type_filter_container = $BackgroundPanel/MainContainer/FiltersContainer/TypeFilters

# 物品显示区域
@onready var scroll_container = $BackgroundPanel/MainContainer/ContentContainer/ScrollContainer
@onready var items_grid = $BackgroundPanel/MainContainer/ContentContainer/ScrollContainer/ItemsGrid

# 筛选状态
var current_size_filter = "all"
var current_type_filter = "all"

# 改装件数据
var all_modifications = []
var filtered_modifications = []

# 改装件库存数据
var inventory_data = {}

# 字体设置
var title_font_size = 28
var filter_font_size = 20
var item_font_size = 16

func _ready():
	print("NewModInventorySystem: 初始化开始")
	
	# 设置背景样式
	setup_background_style()
	
	# 设置字体
	setup_fonts()
	
	# 初始化筛选器
	setup_filters()
	
	# 连接信号
	setup_signals()
	
	# 加载改装件数据
	load_modification_data()
	
	# 初始显示
	refresh_display()
	
	print("NewModInventorySystem: 初始化完成")

# 设置背景样式
func setup_background_style():
	# 创建半透明蓝色背景
	var style_box = StyleBoxFlat.new()
	style_box.bg_color = Color(0.105882, 0.211765, 0.392157, 0.95)
	style_box.border_width_left = 3
	style_box.border_width_top = 3
	style_box.border_width_right = 3
	style_box.border_width_bottom = 3
	style_box.border_color = Color(0.211765, 0.388235, 0.6, 0.9)
	style_box.corner_radius_top_left = 12
	style_box.corner_radius_top_right = 12
	style_box.corner_radius_bottom_right = 12
	style_box.corner_radius_bottom_left = 12
	
	background_panel.add_theme_stylebox_override("panel", style_box)

# 设置字体
func setup_fonts():
	# 标题字体
	title_label.add_theme_font_size_override("font_size", title_font_size)
	title_label.add_theme_color_override("font_color", Color.WHITE)
	
	# 为筛选器按钮设置字体
	for child in size_filter_container.get_children():
		if child is Button:
			child.add_theme_font_size_override("font_size", filter_font_size)
	
	for child in type_filter_container.get_children():
		if child is Button:
			child.add_theme_font_size_override("font_size", filter_font_size)

# 设置筛选器
func setup_filters():
	# 尺寸筛选器
	var size_filters = ["全部", "小型", "中型", "大型"]
	var size_values = ["all", "small", "medium", "large"]
	
	for i in range(size_filters.size()):
		var button = Button.new()
		button.text = size_filters[i]
		button.toggle_mode = true
		button.button_group = ButtonGroup.new() if i == 0 else size_filter_container.get_child(0).button_group
		button.add_theme_font_size_override("font_size", filter_font_size)
		
		if i == 0:  # 默认选中"全部"
			button.button_pressed = true
		
		button.pressed.connect(_on_size_filter_pressed.bind(size_values[i]))
		size_filter_container.add_child(button)
	
	# 类型筛选器
	var type_filters = ["全部", "攻击", "防御", "特殊", "核心"]
	var type_values = ["all", "attack", "defense", "special", "core"]
	
	for i in range(type_filters.size()):
		var button = Button.new()
		button.text = type_filters[i]
		button.toggle_mode = true
		button.button_group = ButtonGroup.new() if i == 0 else type_filter_container.get_child(0).button_group
		button.add_theme_font_size_override("font_size", filter_font_size)
		
		if i == 0:  # 默认选中"全部"
			button.button_pressed = true
		
		button.pressed.connect(_on_type_filter_pressed.bind(type_values[i]))
		type_filter_container.add_child(button)

# 设置信号连接
func setup_signals():
	close_button.pressed.connect(_on_close_pressed)

# 筛选器回调
func _on_size_filter_pressed(filter_value: String):
	current_size_filter = filter_value
	refresh_display()

func _on_type_filter_pressed(filter_value: String):
	current_type_filter = filter_value
	refresh_display()

# 关闭按钮回调
func _on_close_pressed():
	emit_signal("inventory_closed")

# 加载改装件数据
func load_modification_data():
	# 加载新的改装件数据
	var mod_data_class = preload("res://scenes/modifications/data/new_modification_data.gd")
	var all_mod_parts = mod_data_class.get_all_modifications()

	# 转换为字典格式以便UI使用
	all_modifications = []
	for mod_part in all_mod_parts:
		var mod_dict = {
			"id": mod_part.id,
			"name": mod_part.name,
			"description": mod_part.description,
			"type": mod_part.type,
			"size_category": mod_part.size_category,
			"color": mod_part.color,
			"level": mod_part.level,
			"grid_size": mod_part.grid_size,
			"shape_data": mod_part.shape_data,
			"health_bonus": mod_part.health_bonus,
			"shield_bonus": mod_part.shield_bonus,
			"damage_bonus": mod_part.damage_bonus,
			"special_effects": mod_part.special_effects
		}
		all_modifications.append(mod_dict)

	# 初始化库存数据 - 给玩家一些初始配件用于测试
	inventory_data = {
		"basic_energy_core": 1,
		"structural_frame": 1,
		"pulse_cannon": 3,
		"armor_plate": 5,
		"shield_generator": 2,
		"targeting_system": 1,
		"fractal_replicator": 1,
		"heavy_railgun": 1,
		"resonance_field": 1,
		"drone_factory": 1,
		"chaos_shard": 1,
		"mobius_coil": 1,
		"klein_lens": 1,
		"gravity_core": 1
	}

# 刷新显示
func refresh_display():
	# 应用筛选
	apply_filters()
	
	# 清空现有显示
	for child in items_grid.get_children():
		child.queue_free()
	
	# 添加筛选后的物品
	for mod_data in filtered_modifications:
		create_item_display(mod_data)

# 应用筛选
func apply_filters():
	filtered_modifications.clear()
	
	for mod_data in all_modifications:
		# 检查尺寸筛选
		var size_match = current_size_filter == "all"
		if not size_match:
			match current_size_filter:
				"small":
					size_match = mod_data.get("size_category") == "small"
				"medium":
					size_match = mod_data.get("size_category") == "medium"
				"large":
					size_match = mod_data.get("size_category") == "large"
		
		# 检查类型筛选
		var type_match = current_type_filter == "all"
		if not type_match:
			type_match = mod_data.get("type") == current_type_filter
		
		# 如果同时匹配尺寸和类型，添加到筛选结果
		if size_match and type_match:
			filtered_modifications.append(mod_data)

# 创建物品显示
func create_item_display(mod_data):
	var item_panel = Panel.new()
	item_panel.custom_minimum_size = Vector2(120, 150)
	
	# 设置物品样式
	var item_style = StyleBoxFlat.new()
	item_style.bg_color = Color(0.2, 0.3, 0.5, 0.8)
	item_style.border_width_left = 2
	item_style.border_width_top = 2
	item_style.border_width_right = 2
	item_style.border_width_bottom = 2
	item_style.border_color = Color(0.4, 0.6, 0.8, 1.0)
	item_style.corner_radius_top_left = 8
	item_style.corner_radius_top_right = 8
	item_style.corner_radius_bottom_right = 8
	item_style.corner_radius_bottom_left = 8
	
	item_panel.add_theme_stylebox_override("panel", item_style)
	
	# 添加点击事件
	item_panel.gui_input.connect(_on_item_clicked.bind(mod_data))
	
	# 创建内容布局
	var vbox = VBoxContainer.new()
	vbox.anchors_preset = Control.PRESET_FULL_RECT
	
	# 图标区域
	var icon_rect = ColorRect.new()
	icon_rect.color = mod_data.get("color", Color.WHITE)
	icon_rect.custom_minimum_size = Vector2(80, 80)
	icon_rect.size_flags_horizontal = Control.SIZE_SHRINK_CENTER
	
	# 名称标签
	var name_label = Label.new()
	name_label.text = mod_data.get("name", "未知配件")
	name_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	name_label.add_theme_font_size_override("font_size", item_font_size)
	name_label.add_theme_color_override("font_color", Color.WHITE)
	
	# 数量标签
	var quantity = inventory_data.get(mod_data.get("id", ""), 0)
	var quantity_label = Label.new()
	quantity_label.text = "x" + str(quantity)
	quantity_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	quantity_label.add_theme_font_size_override("font_size", item_font_size - 2)
	quantity_label.add_theme_color_override("font_color", Color.YELLOW)
	
	# 组装布局
	vbox.add_child(icon_rect)
	vbox.add_child(name_label)
	vbox.add_child(quantity_label)
	item_panel.add_child(vbox)
	
	items_grid.add_child(item_panel)

# 物品点击事件
func _on_item_clicked(event, mod_data):
	if event is InputEventMouseButton and event.pressed and event.button_index == MOUSE_BUTTON_LEFT:
		emit_signal("mod_selected", mod_data)
		print("选中改装件: " + mod_data.get("name", "未知"))

# 更新库存数据
func update_inventory_data(new_inventory_data):
	inventory_data = new_inventory_data
	refresh_display()

# 添加改装件到库存
func add_modification_to_inventory(mod_id: String, quantity: int = 1):
	if inventory_data.has(mod_id):
		inventory_data[mod_id] += quantity
	else:
		inventory_data[mod_id] = quantity
	refresh_display()

# 从库存移除改装件
func remove_modification_from_inventory(mod_id: String, quantity: int = 1):
	if inventory_data.has(mod_id):
		inventory_data[mod_id] = max(0, inventory_data[mod_id] - quantity)
		if inventory_data[mod_id] == 0:
			inventory_data.erase(mod_id)
	refresh_display()
