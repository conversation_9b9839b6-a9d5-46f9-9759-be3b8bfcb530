extends Node

# 添加必要的类引用
const ModificationDataManager = preload("res://scenes/modifications/data/modification_data_manager.gd")
const TooltipManager = preload("res://scenes/ui/tooltip_manager.gd")

# 游戏管理器
# 负责初始化和管理游戏的全局状态

# 管理器实例
var mod_data_manager = null
var tooltip_manager = null

# 初始化完成标志
var initialization_complete = false

# 初始化
func _ready():
	print("GameManager: 开始初始化...")
	# 延迟初始化，确保所有节点都已准备好
	call_deferred("initialize_game")

# 初始化游戏
func initialize_game():
	print("GameManager: 游戏管理器初始化...")
	
	# 初始化全局管理器
	initialize_managers()
	
	# 初始化游戏数据
	initialize_game_data()
	
	# 初始化配件数据
	initialize_modification_data()
	
	# 初始化提示框管理器
	initialize_tooltip_manager()
	
	# 设置初始化完成标志
	initialization_complete = true
	
	print("GameManager: 游戏管理器初始化完成")

# 初始化全局管理器
func initialize_managers():
	print("GameManager: 初始化全局管理器...")
	# 在这里可以初始化其他全局管理器
	print("GameManager: 全局管理器初始化完成")

# 初始化游戏数据
func initialize_game_data():
	print("GameManager: 初始化游戏数据...")
	# 在这里可以初始化游戏数据
	print("GameManager: 游戏数据初始化完成")

# 初始化配件数据
func initialize_modification_data():
	print("GameManager: 初始化配件数据...")
	
	# 获取配件数据管理器实例
	mod_data_manager = ModificationDataManager.get_instance()
	
	# 将配件数据管理器添加到场景树
	if mod_data_manager and !mod_data_manager.is_inside_tree():
		add_child(mod_data_manager)
		print("GameManager: 配件数据管理器已添加到场景树")
	elif mod_data_manager:
		print("GameManager: 配件数据管理器已存在")
	else:
		push_error("GameManager: 无法获取配件数据管理器实例!")
	
	print("GameManager: 配件数据初始化完成")

# 初始化提示框管理器
func initialize_tooltip_manager():
	print("GameManager: 初始化提示框管理器...")
	
	# 获取提示框管理器实例
	tooltip_manager = TooltipManager.get_instance()
	
	# 将提示框管理器添加到场景树
	if tooltip_manager and !tooltip_manager.is_inside_tree():
		add_child(tooltip_manager)
		print("GameManager: 提示框管理器已添加到场景树")
	elif tooltip_manager:
		print("GameManager: 提示框管理器已存在")
	else:
		push_error("GameManager: 无法获取提示框管理器实例!")
	
	print("GameManager: 提示框管理器初始化完成")
	
# 检查初始化是否完成
func is_initialization_complete():
	return initialization_complete 
