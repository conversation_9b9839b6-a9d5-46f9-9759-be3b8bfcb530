[gd_scene load_steps=2 format=3 uid="uid://c48u7dqj0dnbk"]

[ext_resource type="Script" path="res://scenes/ui/new_mod_inventory_system.gd" id="1_abc123"]

[node name="NewModInventorySystem" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("1_abc123")

[node name="BackgroundPanel" type="Panel" parent="."]
layout_mode = 1
anchors_preset = 7
anchor_left = 0.5
anchor_top = 1.0
anchor_right = 0.5
anchor_bottom = 1.0
offset_left = -640.0
offset_top = -400.0
offset_right = 640.0
grow_horizontal = 2
grow_vertical = 0

[node name="MainContainer" type="VBoxContainer" parent="BackgroundPanel"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 20.0
offset_top = 20.0
offset_right = -20.0
offset_bottom = -20.0
grow_horizontal = 2
grow_vertical = 2

[node name="HeaderContainer" type="HBoxContainer" parent="BackgroundPanel/MainContainer"]
layout_mode = 2
size_flags_vertical = 0

[node name="TitleLabel" type="Label" parent="BackgroundPanel/MainContainer/HeaderContainer"]
layout_mode = 2
size_flags_horizontal = 3
text = "改装件仓库"
horizontal_alignment = 1

[node name="CloseButton" type="Button" parent="BackgroundPanel/MainContainer/HeaderContainer"]
layout_mode = 2
text = "关闭"

[node name="HSeparator" type="HSeparator" parent="BackgroundPanel/MainContainer"]
layout_mode = 2

[node name="FiltersContainer" type="HBoxContainer" parent="BackgroundPanel/MainContainer"]
layout_mode = 2
size_flags_vertical = 0

[node name="SizeFiltersLabel" type="Label" parent="BackgroundPanel/MainContainer/FiltersContainer"]
layout_mode = 2
text = "尺寸:"

[node name="SizeFilters" type="HBoxContainer" parent="BackgroundPanel/MainContainer/FiltersContainer"]
layout_mode = 2

[node name="VSeparator" type="VSeparator" parent="BackgroundPanel/MainContainer/FiltersContainer"]
layout_mode = 2

[node name="TypeFiltersLabel" type="Label" parent="BackgroundPanel/MainContainer/FiltersContainer"]
layout_mode = 2
text = "类型:"

[node name="TypeFilters" type="HBoxContainer" parent="BackgroundPanel/MainContainer/FiltersContainer"]
layout_mode = 2

[node name="HSeparator2" type="HSeparator" parent="BackgroundPanel/MainContainer"]
layout_mode = 2

[node name="ContentContainer" type="VBoxContainer" parent="BackgroundPanel/MainContainer"]
layout_mode = 2
size_flags_vertical = 3

[node name="ScrollContainer" type="ScrollContainer" parent="BackgroundPanel/MainContainer/ContentContainer"]
layout_mode = 2
size_flags_vertical = 3

[node name="ItemsGrid" type="GridContainer" parent="BackgroundPanel/MainContainer/ContentContainer/ScrollContainer"]
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 3
columns = 6
