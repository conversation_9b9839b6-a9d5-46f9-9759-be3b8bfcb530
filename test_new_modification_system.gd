extends Node

# 新改装系统测试脚本
# 用于验证所有组件是否正常工作

func _ready():
	print("=== 新改装系统测试开始 ===")
	
	# 测试改装配件数据
	test_modification_data()
	
	# 测试改装仓库系统
	test_inventory_system()
	
	print("=== 新改装系统测试完成 ===")

func test_modification_data():
	print("\n--- 测试改装配件数据 ---")
	
	# 加载改装配件数据
	var mod_data_class = preload("res://scenes/modifications/data/new_modification_data.gd")
	var all_modifications = mod_data_class.get_all_modifications()
	
	print("总配件数量: " + str(all_modifications.size()))
	
	# 按类型统计
	var type_counts = {}
	var size_counts = {}
	
	for mod in all_modifications:
		# 统计类型
		if type_counts.has(mod.type):
			type_counts[mod.type] += 1
		else:
			type_counts[mod.type] = 1
		
		# 统计尺寸
		if size_counts.has(mod.size_category):
			size_counts[mod.size_category] += 1
		else:
			size_counts[mod.size_category] = 1
		
		# 打印配件信息
		print("配件: " + mod.name + " | 类型: " + mod.type + " | 尺寸: " + mod.size_category + " | 网格: " + str(mod.grid_size))
	
	print("\n类型统计:")
	for type in type_counts.keys():
		print("  " + type + ": " + str(type_counts[type]) + " 个")
	
	print("\n尺寸统计:")
	for size in size_counts.keys():
		print("  " + size + ": " + str(size_counts[size]) + " 个")

func test_inventory_system():
	print("\n--- 测试改装仓库系统 ---")
	
	# 创建仓库系统实例
	var inventory_scene = preload("res://scenes/ui/new_mod_inventory_system.tscn")
	var inventory_instance = inventory_scene.instantiate()
	add_child(inventory_instance)
	
	# 等待一帧让系统初始化
	await get_tree().process_frame
	
	print("仓库系统创建成功")
	print("配件数据加载: " + str(inventory_instance.all_modifications.size()) + " 个配件")
	print("库存数据: " + str(inventory_instance.inventory_data.size()) + " 种配件")
	
	# 测试筛选功能
	inventory_instance.current_size_filter = "small"
	inventory_instance.apply_filters()
	print("小型配件筛选结果: " + str(inventory_instance.filtered_modifications.size()) + " 个")
	
	inventory_instance.current_type_filter = "attack"
	inventory_instance.current_size_filter = "all"
	inventory_instance.apply_filters()
	print("攻击型配件筛选结果: " + str(inventory_instance.filtered_modifications.size()) + " 个")
	
	# 清理
	inventory_instance.queue_free()

func test_visual_system():
	print("\n--- 测试视觉系统 ---")
	
	# 创建测试玩家节点
	var test_player = Node2D.new()
	test_player.name = "TestPlayer"
	add_child(test_player)
	
	# 创建视觉系统
	var visual_system = preload("res://scenes/modifications/visuals/new_mod_visual_system.gd").new()
	test_player.add_child(visual_system)
	visual_system.initialize(test_player)
	
	print("视觉系统创建成功")
	
	# 测试装备配件视觉
	var mod_data_class = preload("res://scenes/modifications/data/new_modification_data.gd")
	var test_mod = mod_data_class.get_modification_by_id("pulse_cannon")
	
	if test_mod:
		visual_system.equip_modification_visual(test_mod, Vector2i(1, 1))
		print("测试配件视觉装备成功: " + test_mod.name)
		
		# 等待动画完成
		await get_tree().create_timer(1.0).timeout
		
		# 卸下配件
		visual_system.unequip_modification_visual(test_mod.id)
		print("测试配件视觉卸下成功")
	
	# 清理
	test_player.queue_free()

func _input(event):
	if event.is_action_pressed("ui_accept"):
		# 按空格键运行视觉系统测试
		test_visual_system()
