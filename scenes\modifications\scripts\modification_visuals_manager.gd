extends Node2D
class_name ModificationVisualsManager

# 改装配件视觉管理器 - 简化版
# 负责管理改装配件的视觉效果和碰撞箱

# 玩家引用
var player_node = null

# 改装配件位置映射表
var positions_map = {
	"1,2": Vector2(-22, 0),  # 左
	"3,2": Vector2(22, 0),   # 右
	"2,1": Vector2(0, -22),  # 上
	"2,3": Vector2(0, 22),   # 下
	"1,1": Vector2(-22, -22),  # 左上
	"3,1": Vector2(22, -22),   # 右上
	"1,3": Vector2(-22, 22),   # 左下
	"3,3": Vector2(22, 22),    # 右下
}

# 当前显示的改装配件视觉效果
var active_visuals = {}

# 碰撞框管理
var collision_shape = null
var initial_collision_size = Vector2(22, 22)
var debug_enabled = true

# 初始化
func _ready():
	# 获取玩家引用
	player_node = get_parent()
	if not player_node:
		push_error("ModificationVisualsManager: 无法找到玩家节点!")
		return
	
	# 获取碰撞形状引用
	collision_shape = player_node.get_node_or_null("CollisionShape2D")
	if not collision_shape:
		push_error("ModificationVisualsManager: 无法找到玩家碰撞形状!")
		return
	
	# 保存初始碰撞框大小
	if collision_shape.shape is RectangleShape2D:
		initial_collision_size = collision_shape.shape.size
	
	debug_log("改装配件视觉管理器初始化完成，初始碰撞箱大小: " + str(initial_collision_size))

# 更新视觉效果
func update_visuals(modifications: Dictionary):
	debug_log("开始更新视觉效果，配件数量: " + str(modifications.size() - 1))  # 减去玩家位置
	
	# 清除所有当前的视觉效果
	clear_all_visuals()
	
	# 重置碰撞箱
	reset_collision_box()
	
	# 处理每个改装配件
	for pos_key in modifications.keys():
		var mod_info = modifications[pos_key]
		
		# 跳过玩家位置
		if mod_info.has("is_player") and mod_info["is_player"]:
			continue
		
		# 创建视觉效果
		create_modification_visual(pos_key, mod_info)
	
	# 只有当有活动的视觉效果时才更新碰撞箱
	if active_visuals.size() > 0:
		update_collision_box()
	
	debug_log("视觉效果更新完成，当前活动配件数: " + str(active_visuals.size()))

# 创建改装配件视觉效果
func create_modification_visual(pos_key: String, mod_info: Dictionary):
	# 检查位置是否有效
	if not positions_map.has(pos_key):
		push_error("ModificationVisualsManager: 无效的改装配件位置: " + pos_key)
		return
	
	# 获取位置
	var mod_position = positions_map[pos_key]
	
	# 获取改装件信息
	var mod_id = mod_info["name"]
	var mod_color = mod_info.get("color", Color(0.3, 0.7, 1.0))
	var mod_size = mod_info.get("size", Vector2(15, 15))
	
	# 创建视觉效果
	var mod_visual_script = load("res://scenes/modifications/scripts/modification_visual.gd")
	var visual = mod_visual_script.new(mod_id, mod_color, mod_position, mod_size)
	visual.name = "ModVisual_" + pos_key
	add_child(visual)
	
	# 存储视觉效果
	active_visuals[pos_key] = visual
	
	debug_log("已创建改装配件视觉效果: " + mod_id + " 在位置 " + pos_key)

# 清除所有视觉效果
func clear_all_visuals():
	debug_log("清除所有视觉效果，当前数量: " + str(active_visuals.size()))
	
	# 移除所有视觉效果节点
	for pos_key in active_visuals.keys():
		var visual = active_visuals[pos_key]
		if is_instance_valid(visual):
			visual.queue_free()
	
	# 清空活动视觉效果字典
	active_visuals.clear()
	
	debug_log("视觉效果清除完成")

# 重置碰撞箱到初始大小
func reset_collision_box():
	if not collision_shape or not collision_shape.shape is RectangleShape2D:
		return
		
	var current_size = collision_shape.shape.size
	collision_shape.shape.size = initial_collision_size

	debug_log("碰撞箱已重置: " + str(current_size) + " -> " + str(initial_collision_size))

# 更新碰撞箱以包含所有改装配件
func update_collision_box():
	if not collision_shape or not collision_shape.shape is RectangleShape2D:
		push_error("ModificationVisualsManager: 无法更新碰撞箱，碰撞形状无效!")
		return
	
	# 计算最大边界（起始为初始碰撞箱大小）
	var max_bounds = Vector2(initial_collision_size.x, initial_collision_size.y)
	
	# 检查每个活动的视觉效果
	for pos_key in active_visuals.keys():
		var visual = active_visuals[pos_key]
		var mod_position = positions_map[pos_key]
		
		# 确保视觉效果有效
		if not is_instance_valid(visual):
			debug_log("警告：视觉效果无效，位置: " + pos_key)
			continue
		
		# 更新水平范围
		var x_extent = abs(mod_position.x) + visual.mod_size.x / 2.0
		if max_bounds.x < x_extent * 2.0:
			max_bounds.x = x_extent * 2.0
		
		# 更新垂直范围
		var y_extent = abs(mod_position.y) + visual.mod_size.y / 2.0
		if max_bounds.y < y_extent * 2.0:
			max_bounds.y = y_extent * 2.0
	
	var old_size = collision_shape.shape.size
	
	# 应用新的碰撞箱大小
	collision_shape.shape.size = max_bounds

	debug_log("碰撞箱已更新: " + str(old_size) + " -> " + str(max_bounds))

# 获取当前碰撞箱大小
func get_collision_size() -> Vector2:
	if collision_shape and collision_shape.shape is RectangleShape2D:
		return collision_shape.shape.size
	return Vector2.ZERO

# 检查位置是否被占用
func is_position_occupied(pos_key: String) -> bool:
	return active_visuals.has(pos_key) and is_instance_valid(active_visuals[pos_key])

# 获取指定位置的改装配件
func get_modification_at(pos_key: String):
	if is_position_occupied(pos_key):
		return active_visuals[pos_key]
	return null

# 调试日志
func debug_log(message: String):
	if debug_enabled:
		print("ModVisualsManager: " + message)

# 强制刷新碰撞箱和视觉效果
func force_refresh():
	debug_log("执行强制刷新")
	
	# 获取当前配件信息
	var mods_backup = {}
	for pos_key in active_visuals.keys():
		var visual = active_visuals[pos_key]
		if is_instance_valid(visual):
			mods_backup[pos_key] = {
				"name": visual.mod_id,
				"color": visual.mod_color,
				"size": visual.mod_size
			}
	
	# 清除并重建
	clear_all_visuals()
	reset_collision_box()
	
	# 重新创建
	for pos_key in mods_backup.keys():
		create_modification_visual(pos_key, mods_backup[pos_key])
	
	# 更新碰撞箱
	if mods_backup.size() > 0:
		update_collision_box()
		
	debug_log("强制刷新完成") 
