[gd_scene load_steps=4 format=3 uid="uid://bmogmcko8lfev"]

[ext_resource type="Script" path="res://scenes/ui/new_modification_controller.gd" id="1_abc124"]
[ext_resource type="PackedScene" path="res://scenes/ui/new_mod_inventory_system.tscn" id="2_abc125"]

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_1"]
bg_color = Color(0.05, 0.05, 0.1, 0.95)
border_width_left = 3
border_width_top = 3
border_width_right = 3
border_width_bottom = 3
border_color = Color(0.2, 0.4, 0.8, 1)
corner_radius_top_left = 10
corner_radius_top_right = 10
corner_radius_bottom_right = 10
corner_radius_bottom_left = 10

[node name="NewModificationScreen" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("1_abc124")

[node name="Background" type="Panel" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme_override_styles/panel = SubResource("StyleBoxFlat_1")

[node name="MainContainer" type="HBoxContainer" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 20.0
offset_top = 20.0
offset_right = -20.0
offset_bottom = -20.0
grow_horizontal = 2
grow_vertical = 2

[node name="LeftPanel" type="VBoxContainer" parent="MainContainer"]
layout_mode = 2
size_flags_horizontal = 3
size_flags_stretch_ratio = 0.6

[node name="HeaderContainer" type="HBoxContainer" parent="MainContainer/LeftPanel"]
layout_mode = 2
size_flags_vertical = 0

[node name="TitleLabel" type="Label" parent="MainContainer/LeftPanel/HeaderContainer"]
layout_mode = 2
size_flags_horizontal = 3
theme_override_font_sizes/font_size = 32
text = "飞船改装系统"
horizontal_alignment = 1

[node name="CloseButton" type="Button" parent="MainContainer/LeftPanel/HeaderContainer"]
layout_mode = 2
theme_override_font_sizes/font_size = 24
text = "关闭 [ESC]"

[node name="HSeparator" type="HSeparator" parent="MainContainer/LeftPanel"]
layout_mode = 2

[node name="GridContainer" type="VBoxContainer" parent="MainContainer/LeftPanel"]
layout_mode = 2
size_flags_vertical = 3

[node name="GridTitle" type="Label" parent="MainContainer/LeftPanel/GridContainer"]
layout_mode = 2
theme_override_font_sizes/font_size = 24
text = "改装网格 (9x11)"
horizontal_alignment = 1

[node name="GridPanel" type="Panel" parent="MainContainer/LeftPanel/GridContainer"]
layout_mode = 2
size_flags_vertical = 3

[node name="ModificationGrid" type="GridContainer" parent="MainContainer/LeftPanel/GridContainer/GridPanel"]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -180.0
offset_top = -220.0
offset_right = 180.0
offset_bottom = 220.0
grow_horizontal = 2
grow_vertical = 2
columns = 9

[node name="VSeparator" type="VSeparator" parent="MainContainer"]
layout_mode = 2

[node name="RightPanel" type="VBoxContainer" parent="MainContainer"]
layout_mode = 2
size_flags_horizontal = 3
size_flags_stretch_ratio = 0.4

[node name="StatsContainer" type="VBoxContainer" parent="MainContainer/RightPanel"]
layout_mode = 2
size_flags_vertical = 0

[node name="StatsTitle" type="Label" parent="MainContainer/RightPanel/StatsContainer"]
layout_mode = 2
theme_override_font_sizes/font_size = 24
text = "飞船状态"
horizontal_alignment = 1

[node name="StatsPanel" type="Panel" parent="MainContainer/RightPanel/StatsContainer"]
layout_mode = 2
custom_minimum_size = Vector2(0, 120)

[node name="StatsGrid" type="GridContainer" parent="MainContainer/RightPanel/StatsContainer/StatsPanel"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 10.0
offset_top = 10.0
offset_right = -10.0
offset_bottom = -10.0
grow_horizontal = 2
grow_vertical = 2
columns = 2

[node name="HealthLabel" type="Label" parent="MainContainer/RightPanel/StatsContainer/StatsPanel/StatsGrid"]
layout_mode = 2
theme_override_font_sizes/font_size = 18
text = "生命值:"

[node name="HealthValue" type="Label" parent="MainContainer/RightPanel/StatsContainer/StatsPanel/StatsGrid"]
layout_mode = 2
theme_override_font_sizes/font_size = 18
theme_override_colors/font_color = Color(1, 0.5, 0.5, 1)
text = "10 / 10"

[node name="ShieldLabel" type="Label" parent="MainContainer/RightPanel/StatsContainer/StatsPanel/StatsGrid"]
layout_mode = 2
theme_override_font_sizes/font_size = 18
text = "护盾值:"

[node name="ShieldValue" type="Label" parent="MainContainer/RightPanel/StatsContainer/StatsPanel/StatsGrid"]
layout_mode = 2
theme_override_font_sizes/font_size = 18
theme_override_colors/font_color = Color(0.5, 0.5, 1, 1)
text = "5 / 5"

[node name="PowerLabel" type="Label" parent="MainContainer/RightPanel/StatsContainer/StatsPanel/StatsGrid"]
layout_mode = 2
theme_override_font_sizes/font_size = 18
text = "能量:"

[node name="PowerValue" type="Label" parent="MainContainer/RightPanel/StatsContainer/StatsPanel/StatsGrid"]
layout_mode = 2
theme_override_font_sizes/font_size = 18
theme_override_colors/font_color = Color(1, 1, 0.5, 1)
text = "100 / 150"

[node name="MassLabel" type="Label" parent="MainContainer/RightPanel/StatsContainer/StatsPanel/StatsGrid"]
layout_mode = 2
theme_override_font_sizes/font_size = 18
text = "质量:"

[node name="MassValue" type="Label" parent="MainContainer/RightPanel/StatsContainer/StatsPanel/StatsGrid"]
layout_mode = 2
theme_override_font_sizes/font_size = 18
theme_override_colors/font_color = Color(0.8, 0.8, 0.8, 1)
text = "25 / 100"

[node name="HSeparator2" type="HSeparator" parent="MainContainer/RightPanel"]
layout_mode = 2

[node name="InfoContainer" type="VBoxContainer" parent="MainContainer/RightPanel"]
layout_mode = 2
size_flags_vertical = 3

[node name="InfoTitle" type="Label" parent="MainContainer/RightPanel/InfoContainer"]
layout_mode = 2
theme_override_font_sizes/font_size = 24
text = "配件信息"
horizontal_alignment = 1

[node name="InfoPanel" type="Panel" parent="MainContainer/RightPanel/InfoContainer"]
layout_mode = 2
size_flags_vertical = 3

[node name="InfoContent" type="VBoxContainer" parent="MainContainer/RightPanel/InfoContainer/InfoPanel"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 10.0
offset_top = 10.0
offset_right = -10.0
offset_bottom = -10.0
grow_horizontal = 2
grow_vertical = 2

[node name="ModNameLabel" type="Label" parent="MainContainer/RightPanel/InfoContainer/InfoPanel/InfoContent"]
layout_mode = 2
theme_override_font_sizes/font_size = 20
theme_override_colors/font_color = Color(1, 1, 0.5, 1)
text = "选择一个配件查看详情"
horizontal_alignment = 1

[node name="ModDescLabel" type="Label" parent="MainContainer/RightPanel/InfoContainer/InfoPanel/InfoContent"]
layout_mode = 2
theme_override_font_sizes/font_size = 16
text = "从下方的改装仓库中选择配件，然后点击网格中的位置进行装备。"
autowrap_mode = 3

[node name="ModStatsLabel" type="Label" parent="MainContainer/RightPanel/InfoContainer/InfoPanel/InfoContent"]
layout_mode = 2
theme_override_font_sizes/font_size = 14
theme_override_colors/font_color = Color(0.8, 0.8, 1, 1)
text = ""
autowrap_mode = 3

[node name="InventorySystem" parent="." instance=ExtResource("2_abc125")]
layout_mode = 1
anchors_preset = 7
anchor_left = 0.5
anchor_top = 1.0
anchor_right = 0.5
anchor_bottom = 1.0
offset_left = -640.0
offset_top = -400.0
offset_right = 640.0
grow_horizontal = 2
grow_vertical = 0
