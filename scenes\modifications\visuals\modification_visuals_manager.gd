extends Node2D
class_name ModVisualsManager

# 改装配件视觉管理器
# 负责管理玩家身上所有改装配件的视觉效果

# 信号
signal visual_added(mod_id)
signal visual_removed(mod_id)

# 存储所有视觉效果节点
var visuals = {}  # 格式: {mod_id: visual_node}

# 初始化
func _ready():
	pass

# 添加配件视觉效果
func add_modification_visual(mod_id: String, mod_data, position: Vector2 = Vector2.ZERO) -> Node:
	# 如果已存在，先移除
	if visuals.has(mod_id):
		remove_modification_visual(mod_id)
	
	# 创建新的视觉效果
	var visual = ModVisual.new()
	visual.name = "ModVisual_" + mod_id
	visual.position = position
	visual.set_modification_data(mod_data)
	
	# 添加到场景
	add_child(visual)
	
	# 存储引用
	visuals[mod_id] = visual
	
	# 发送信号
	emit_signal("visual_added", mod_id)
	
	return visual

# 移除配件视觉效果
func remove_modification_visual(mod_id: String) -> void:
	if visuals.has(mod_id):
		var visual = visuals[mod_id]
		visual.remove()
		visuals.erase(mod_id)
		emit_signal("visual_removed", mod_id)

# 更新配件视觉效果
func update_modification_visual(mod_id: String, mod_data) -> void:
	if visuals.has(mod_id):
		visuals[mod_id].set_modification_data(mod_data)

# 获取配件视觉效果
func get_modification_visual(mod_id: String) -> Node:
	if visuals.has(mod_id):
		return visuals[mod_id]
	return null

# 移动配件视觉效果
func move_modification_visual(mod_id: String, new_position: Vector2) -> void:
	if visuals.has(mod_id):
		var visual = visuals[mod_id]
		
		# 创建移动动画
		var tween = create_tween()
		tween.tween_property(visual, "position", new_position, 0.3)
	
# 闪烁配件视觉效果
func flash_modification_visual(mod_id: String, duration: float = 0.5) -> void:
	if visuals.has(mod_id):
		var visual = visuals[mod_id]
		var visual_node = visual.get_visual_node()
		if visual_node:
			# 创建闪烁动画
			var tween = create_tween()
			tween.set_loops(5)  # 闪烁5次
			tween.tween_property(visual_node, "modulate:a", 0.5, duration/10)
			tween.tween_property(visual_node, "modulate:a", 1.0, duration/10)

# 清除所有视觉效果
func clear_all_visuals() -> void:
	for mod_id in visuals.keys():
		remove_modification_visual(mod_id)
	
# 根据网格位置计算世界位置
func grid_to_world_position(grid_x: int, grid_y: int, cell_size: Vector2 = Vector2(20, 20), offset: Vector2 = Vector2.ZERO) -> Vector2:
	return Vector2(grid_x * cell_size.x, grid_y * cell_size.y) + offset 
