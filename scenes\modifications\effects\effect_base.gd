extends Resource
class_name ModificationEffect

# 效果类型枚举
enum EffectType {
	NONE,
	SHIELD_BONUS,
	HEALTH_BONUS,
	SPEED_BONUS,
	DAMAGE_BONUS,
	FIRE_RATE_BONUS,
	CUSTOM
}

# 效果属性
var type: int = EffectType.NONE
var value: float = 0.0
var description: String = ""
var id: String = ""

# 创建效果的静态方法
static func create(effect_type: int, effect_value: float = 0.0, effect_id: String = "", effect_description: String = "") -> ModificationEffect:
	var effect = ModificationEffect.new()
	effect.type = effect_type
	effect.value = effect_value
	effect.id = effect_id
	
	# 如果没有提供描述，则根据类型生成默认描述
	if effect_description.is_empty():
		effect.description = effect.get_default_description()
	else:
		effect.description = effect_description
		
	return effect

# 获取效果描述
func get_description() -> String:
	if not description.is_empty():
		return description
	return get_default_description()

# 根据类型获取默认描述
func get_default_description() -> String:
	match type:
		EffectType.SHIELD_BONUS:
			return "护盾 +%d" % int(value)
		EffectType.HEALTH_BONUS:
			return "生命 +%d" % int(value)
		EffectType.SPEED_BONUS:
			return "速度 +%.0f%%" % (value * 100)
		EffectType.DAMAGE_BONUS:
			return "伤害 +%.0f%%" % (value * 100)
		EffectType.FIRE_RATE_BONUS:
			return "射速 +%.0f%%" % (value * 100)
		_:
			return "未知效果"

# 应用效果到玩家
func apply(player_node) -> void:
	if not player_node:
		push_error("ModificationEffect: 无法应用效果，玩家节点为空!")
		return
	
	# 根据效果类型应用不同的效果
	match type:
		EffectType.SHIELD_BONUS:
			# 增加护盾上限
			if player_node.has_method("add_shield_point"):
				for i in range(int(value)):
					player_node.add_shield_point()
			else:
				player_node.max_shield_points += int(value)
				print("应用护盾加成: +%d" % int(value))
		
		EffectType.HEALTH_BONUS:
			# 增加生命上限
			player_node.max_hp += int(value)
			print("应用生命加成: +%d" % int(value))
		
		EffectType.SPEED_BONUS:
			# 增加移动速度
			player_node.base_move_speed *= (1.0 + value)
			print("应用速度加成: +%.0f%%" % (value * 100))
		
		EffectType.DAMAGE_BONUS:
			# 增加伤害
			player_node.bullet_damage_multiplier = 1.0 + value
			print("应用伤害加成: +%.0f%%" % (value * 100))
		
		EffectType.FIRE_RATE_BONUS:
			# 提高射速
			player_node.base_fire_rate *= (1.0 - value)  # 减少射击间隔
			player_node.update_fire_rate()
			print("应用射速加成: +%.0f%%" % (value * 100))
		
		EffectType.CUSTOM:
			# 自定义效果，可以通过扩展子类实现
			print("应用自定义效果")
		
		_:
			print("未知效果类型，无法应用")

# 移除效果
func remove(player_node) -> void:
	if not player_node:
		push_error("ModificationEffect: 无法移除效果，玩家节点为空!")
		return
	
	# 根据效果类型移除不同的效果
	match type:
		EffectType.SHIELD_BONUS:
			# 减少护盾上限
			player_node.max_shield_points -= int(value)
			# 确保当前护盾值不超过上限
			player_node.shield_points = min(player_node.shield_points, player_node.max_shield_points)
			print("移除护盾加成: -%d" % int(value))
		
		EffectType.HEALTH_BONUS:
			# 减少生命上限
			player_node.max_hp -= int(value)
			# 确保当前生命值不超过上限
			player_node.hp = min(player_node.hp, player_node.max_hp)
			print("移除生命加成: -%d" % int(value))
		
		EffectType.SPEED_BONUS:
			# 恢复基础移动速度
			player_node.base_move_speed /= (1.0 + value)
			print("移除速度加成: -%.0f%%" % (value * 100))
		
		EffectType.DAMAGE_BONUS:
			# 恢复基础伤害
			player_node.bullet_damage_multiplier = 1.0
			print("移除伤害加成: -%.0f%%" % (value * 100))
		
		EffectType.FIRE_RATE_BONUS:
			# 恢复基础射速
			player_node.base_fire_rate /= (1.0 - value)  # 恢复射击间隔
			player_node.update_fire_rate()
			print("移除射速加成: -%.0f%%" % (value * 100))
		
		EffectType.CUSTOM:
			# 自定义效果，可以通过扩展子类实现
			print("移除自定义效果")
		
		_:
			print("未知效果类型，无法移除") 