extends Control

# 修改界面控制器
# 当玩家按下B键时显示/隐藏，包含5x5网格和底部物品栏

# 信号
signal modification_screen_opened
signal modification_screen_closed

# 界面组件引用
@onready var grid_panel = %GridPanel
@onready var grid_container = %GridContainer
@onready var player_placeholder = %PlayerPlaceholder
@onready var animation_container = $AnimationContainer

# 脚本引用
const ModInventoryItemScript = preload("res://scenes/ui/mod_inventory_item.gd")
const ModGridCellScript = preload("res://scenes/ui/mod_grid_cell.gd")

# 新物品栏引用
const ModInventoryPanelScene = preload("res://scenes/ui/mod_inventory_panel.tscn")
var mod_inventory_panel = null

# 常量定义
const PART_SCALE_FACTOR = 0.9
const PLAYER_SCALE_FACTOR = 0.7
const MODIFICATION_SCREEN_SCALE = 0.75  # 从0.85减小到0.75，进一步缩小界面

# 改装件数据
var placeholder_mods = [
	{"name": "测试配件1", "color": Color(0.3, 0.7, 1.0)},
	{"name": "测试配件2", "color": Color(0.3, 0.7, 1.0)},
	{"name": "测试配件3", "color": Color(0.3, 0.7, 1.0)},
	{"name": "测试配件4", "color": Color(0.3, 0.7, 1.0)},
	{"name": "测试配件5", "color": Color(0.3, 0.7, 1.0)}
]

# 占据的网格映射 - 用于追踪哪些格子已被占据
var occupied_cells = {}
# 玩家位置 - 中心位置
var player_position = Vector2(5, 4)  # 11x9网格的中心位置

# 界面状态
var is_open: bool = false
var is_animating: bool = false

# 网格单元格预设
const CELL_SIZE = Vector2(120, 120)  # 从100x100增加到120x120
const CELL_COUNT = 99  # 11x9
const GRID_SIZE = 11  # 列数

# 添加顶部预加载声明
const ModInventoryClass = preload("res://scenes/modifications/data/mod_inventory.gd")
# 移除视觉管理器相关引用
const ModificationResourceClass = preload("res://scenes/modifications/data/modification_resource.gd")

# 初始化
func _ready():
	# 初始状态为隐藏
	visible = false
	
	# 缩小到0大小（准备后续动画）
	grid_panel.scale = Vector2.ZERO
	
	# 应用整体缩放
	scale = Vector2.ONE * MODIFICATION_SCREEN_SCALE
	
	# 确保背景覆盖整个屏幕，不受缩放影响
	$Background.scale = Vector2.ONE / MODIFICATION_SCREEN_SCALE  # 反向缩放以抵消父节点缩放
	$Background.anchor_right = 1.0 / MODIFICATION_SCREEN_SCALE
	$Background.anchor_bottom = 1.0 / MODIFICATION_SCREEN_SCALE
	$Background.mouse_filter = Control.MOUSE_FILTER_STOP
	
	# 初始化网格
	setup_grid()
	
	# 尝试加载已保存的改装状态
	load_modification_state()
	
	# 确保根节点可以接收拖放
	mouse_filter = Control.MOUSE_FILTER_STOP
	
	# 创建一个全屏的透明面板来捕获所有拖放事件
	var panel_bg = ColorRect.new()
	panel_bg.color = Color(0, 0, 0, 0.01)  # 几乎完全透明
	panel_bg.mouse_filter = Control.MOUSE_FILTER_PASS  # 确保鼠标事件传递
	panel_bg.anchors_preset = Control.PRESET_FULL_RECT
	panel_bg.z_index = -100  # 确保在最底层
	add_child(panel_bg)
	
	# 开启拖放处理（确保这个节点可以接收拖放）
	set_process_input(true)
	
	# 初始化新的物品栏
	_setup_new_inventory()

# 设置新物品栏
func _setup_new_inventory():
	# 创建新的物品栏界面
	mod_inventory_panel = ModInventoryPanelScene.instantiate()
	add_child(mod_inventory_panel)
	
	# 调整物品栏的位置，向右下角移动
	var screen_size = get_viewport_rect().size
	mod_inventory_panel.size.y = 200 # 确保高度与panel设置一致
	
	# 设置锚点和偏移，确保位于右下角
	mod_inventory_panel.anchors_preset = 12
	mod_inventory_panel.anchor_top = 1.0
	mod_inventory_panel.anchor_right = 1.0
	mod_inventory_panel.anchor_bottom = 1.0
	mod_inventory_panel.offset_top = -200.0
	mod_inventory_panel.grow_horizontal = 2
	mod_inventory_panel.grow_vertical = 0
	
	# 默认隐藏物品栏
	mod_inventory_panel.visible = false
	
	# 连接物品被选中的信号
	mod_inventory_panel.mod_item_selected.connect(_on_mod_item_selected)
	
	print("修改界面: 已初始化新的物品栏")

# 物品选中回调
func _on_mod_item_selected(mod_resource):
	print("修改界面: 已选中改装件 " + mod_resource.name)
	# 后续可以实现更多选中后的功能

# 设置网格
func setup_grid():
	# 清空现有网格
	for child in grid_container.get_children():
		child.queue_free()
	
	# 清空占据格子映射
	occupied_cells.clear()
	
	# 创建11x9网格
	grid_container.columns = GRID_SIZE  # 设置为11列
	
	# 计算行数
	var row_count = 9  # 固定为9行
	
	# 更新玩家位置为网格中心
	player_position = Vector2(GRID_SIZE / 2, row_count / 2)
	if GRID_SIZE % 2 == 0:
		player_position.x -= 0.5
	if row_count % 2 == 0:
		player_position.y -= 0.5
		
	# 四舍五入确保位置是整数
	player_position = Vector2(round(player_position.x), round(player_position.y))
	
	print("玩家位置设置为: ", player_position)
	
	# 创建网格单元格
	for i in range(GRID_SIZE * row_count):
		var cell = Panel.new()
		cell.custom_minimum_size = CELL_SIZE
		cell.size_flags_horizontal = Control.SIZE_FILL
		cell.size_flags_vertical = Control.SIZE_FILL
		
		# 计算行列位置（用于后续处理）
		var row = i / GRID_SIZE
		var col = i % GRID_SIZE
		
		# 应用网格单元格脚本
		cell.set_script(ModGridCellScript)
		cell.grid_position = Vector2(col, row)
		
		# 保存对修改界面的引用，以便调用连通性检查
		cell.modification_screen = self
		
		# 如果是中心单元格，标记为玩家位置
		if Vector2(col, row) == player_position:
			cell.modulate = Color(0.3, 0.3, 0.3, 0.5)  # 半透明
			# 暂时禁用玩家位置的拖放功能
			cell.mouse_filter = Control.MOUSE_FILTER_IGNORE
			# 标记中心为玩家位置 - 默认已占据
			occupied_cells[Vector2(col, row)] = {"is_player": true}
		
		grid_container.add_child(cell)

	# 在所有网格单元格创建完毕后，单独处理玩家占位符
	if player_placeholder:
		# 调整玩家占位符大小
		var placeholder_size = 70
		player_placeholder.size = Vector2(placeholder_size, placeholder_size)
	
		# 应用玩家模型缩放
		player_placeholder.scale = Vector2.ONE * PLAYER_SCALE_FACTOR
		
		# 计算中心单元格在GridPanel中的实际位置
		var center_cell_index = int(player_position.y) * GRID_SIZE + int(player_position.x)
		if center_cell_index < grid_container.get_child_count():
			var center_cell = grid_container.get_child(center_cell_index)
	
			# 获取中心单元格的全局位置
			var center_cell_global_pos = center_cell.global_position
			var grid_panel_global_pos = grid_panel.global_position
			
			# 计算中心单元格相对于GridPanel的位置
			var relative_pos = center_cell_global_pos - grid_panel_global_pos
			
			# 计算中心单元格的中心点
			relative_pos += center_cell.size / 2
			
			# 设置玩家占位符位置，使其居中于中心单元格
			player_placeholder.global_position = center_cell_global_pos + center_cell.size / 2 - player_placeholder.size * player_placeholder.scale / 2
			
			print("中心单元格全局位置: ", center_cell_global_pos)
			print("玩家占位符全局位置: ", player_placeholder.global_position)
		else:
			# 如果找不到中心单元格，直接放在GridPanel中央
			player_placeholder.position = grid_panel.size / 2 - player_placeholder.size * player_placeholder.scale / 2
			print("未找到中心单元格，将玩家占位符放在GridPanel中央")

# 切换修改界面显示状态
func toggle():
	if is_animating:
		return
		
	if is_open:
		close()
	else:
		open()

# 打开修改界面
func open():
	if is_open or is_animating:
		return
		
	is_animating = true
	
	# 显示界面
	visible = true
	
	# 设置现有网格格子的状态
	update_grid_cells_from_occupied()
	
	# 更新网格可放置状态视觉提示
	update_grid_placement_visuals()
	
	# 显示新的物品栏并调整位置
	mod_inventory_panel.visible = true
	
	# 获取视口尺寸用于位置计算
	var viewport_size = get_viewport_rect().size
	
	# 确保改装仓库位于右下角
	mod_inventory_panel.anchors_preset = 12
	mod_inventory_panel.anchor_top = 1.0
	mod_inventory_panel.anchor_right = 1.0
	mod_inventory_panel.anchor_bottom = 1.0
	mod_inventory_panel.offset_top = -200.0
	mod_inventory_panel.grow_horizontal = 2
	mod_inventory_panel.grow_vertical = 0
	
	# 进入慢动作模式而不是暂停游戏
	Engine.time_scale = 0.1  # 降低游戏速度到10%
	
	# 开始打开动画 - 补偿慢动作影响
	var time_compensation = 1.0 / Engine.time_scale  # 补偿因子
	var initial_delay = 0.3 / time_compensation  # 初始显示延迟
	var scale_duration = 0.6 / time_compensation  # 缩放动画时间 - 加快0.2秒
	
	# 确保背景覆盖整个屏幕
	$Background.size = viewport_size * (1.0 / MODIFICATION_SCREEN_SCALE)
	$Background.position = Vector2.ZERO
	
	# 确保整个界面在屏幕中央，向右上方调整
	animation_container.position = Vector2(
		viewport_size.x * (1 - MODIFICATION_SCREEN_SCALE) / 2 + 30,  # 向右偏移30像素
		viewport_size.y * (1 - MODIFICATION_SCREEN_SCALE) / 2 - 60   # 向上偏移60像素
	)
	
	# 确保网格面板位于屏幕正中央，略微向右上角偏移以补偿缩放效果
	var grid_size = grid_panel.size
	grid_panel.position = Vector2(
		viewport_size.x / 2 - grid_size.x / 2 + 70,  # 向右偏移70像素
		viewport_size.y / 2 - grid_size.y / 2 - 70   # 向上偏移70像素
	)
	
	# 确保网格面板的中心点是从中心开始扩展的
	grid_panel.pivot_offset = grid_panel.size/2
	
	# 设置初始缩放 - 从一个很小的点开始
	grid_panel.scale = Vector2(0.05, 0.05)  # 非常小的初始大小
	
	# 创建序列补间
	var sequence = create_tween()
	
	# 先停顿短暂时间
	sequence.tween_interval(initial_delay)
	
	# 网格从中心点向四周扩展 - 加快速度
	sequence.tween_property(grid_panel, "scale", Vector2(1, 1), scale_duration).set_ease(Tween.EASE_OUT).set_trans(Tween.TRANS_BACK)
	
	# 动画完成后更新状态
	sequence.tween_callback(func():
		is_animating = false
		is_open = true
		emit_signal("modification_screen_opened")
	)

# 从已占据的格子数据更新网格格子显示
func update_grid_cells_from_occupied():
	# 清空所有现有网格内容
	for i in range(grid_container.get_child_count()):
		var cell = grid_container.get_child(i)
		if cell.has_method("clear_modification_silent"):
			cell.clear_modification_silent()
	
	# 根据占据状态重新填充网格
	for pos in occupied_cells.keys():
		# 跳过玩家位置
		if occupied_cells[pos].has("is_player") and occupied_cells[pos]["is_player"]:
			continue
			
		# 从已保存数据中恢复改装件显示
		if occupied_cells[pos].has("name") and occupied_cells[pos].has("color"):
			# 找到对应的网格格子
			for i in range(grid_container.get_child_count()):
				var cell = grid_container.get_child(i)
				if cell.grid_position == pos:
					# 放置改装件
					cell.place_modification(occupied_cells[pos]["name"], occupied_cells[pos]["color"])
					break

# 关闭修改界面
func close():
	if !is_open or is_animating:
		return
		
	is_animating = true
	
	# 保存当前改装状态到全局状态（如果需要）
	save_modification_state()
	
	# 隐藏新的物品栏
	mod_inventory_panel.visible = false
	
	# 获取视口尺寸
	var viewport_size = get_viewport_rect().size
	
	# 计算动画时间补偿
	var time_compensation = 1.0 / Engine.time_scale  # 补偿因子
	var animation_duration = 0.55 / time_compensation   # 动画时间 - 减慢0.2秒
	
	# 获取玩家位置 - 改进定位方式
	var player_node = get_tree().get_first_node_in_group("player")
	var target_pos = grid_panel.position  # 默认不移动

	if player_node:
		# 获取玩家的全局变换
		var canvas = get_canvas_transform()
		var global_pos = player_node.global_position
		
		# 转换为屏幕坐标并调整偏移，确保准确定位到玩家中心
		target_pos = global_pos
		
		# 打印调试信息，帮助排查位置问题
		print("玩家位置: ", global_pos)
		print("目标位置: ", target_pos)
		
		# 设置缩小后的大小以匹配玩家大小
		var player_size = 40  # 估计玩家尺寸，从30增加到40
		var final_scale = player_size / grid_panel.size.x
		
		# 创建并行补间 - 同时缩小和移动
		var tween = create_tween().set_parallel()
		
		# 先移动面板的枢轴点到左上角，以便能正确缩放到玩家位置
		grid_panel.pivot_offset = Vector2.ZERO
		
		# 调整目标位置，考虑面板大小
		var offset_x = grid_panel.size.x * final_scale / 2
		var offset_y = grid_panel.size.y * final_scale / 2
		target_pos = target_pos - Vector2(offset_x, offset_y)
		
		# 同时缩小并移动到玩家位置 - 减慢速度
		tween.tween_property(grid_panel, "scale", Vector2(final_scale, final_scale), animation_duration).set_ease(Tween.EASE_IN).set_trans(Tween.TRANS_BACK)
		tween.tween_property(grid_panel, "position", target_pos, animation_duration).set_ease(Tween.EASE_IN).set_trans(Tween.TRANS_QUINT)
		
		# 动画完成后更新状态
		tween.chain().tween_callback(func():
			is_animating = false
			is_open = false
			visible = false
			
			# 恢复正常游戏速度
			Engine.time_scale = 1.0
			
			# 确保发送信号，让玩家更新改装状态
			emit_signal("modification_screen_closed")
		)
	else:
		# 如果找不到玩家，仍然执行关闭动画
		var tween = create_tween().set_parallel()
		
		# 简单缩小到中心点 - 减慢速度
		tween.tween_property(grid_panel, "scale", Vector2(0.05, 0.05), animation_duration).set_ease(Tween.EASE_IN).set_trans(Tween.TRANS_BACK)
		
		# 动画完成后更新状态
		tween.chain().tween_callback(func():
			is_animating = false
			is_open = false
			visible = false
			
			# 恢复正常游戏速度
			Engine.time_scale = 1.0
			
			# 确保发送信号，让玩家更新改装状态
			emit_signal("modification_screen_closed")
		)

# 保存当前改装状态到全局状态
func save_modification_state():
	# 检查是否有全局游戏状态管理器
	var game_state = get_tree().get_first_node_in_group("game_state")
	if game_state and game_state.has_method("save_modification_state"):
		# 准备一个可序列化的改装状态
		var serializable_state = {}
		for pos in occupied_cells.keys():
			# 跳过玩家位置
			if occupied_cells[pos].has("is_player") and occupied_cells[pos]["is_player"]:
				continue
				
			# 只保存名称和颜色信息
			if occupied_cells[pos].has("name") and occupied_cells[pos].has("color"):
				# 将Vector2键转换为字符串，方便序列化
				var pos_key = str(pos.x) + "," + str(pos.y)
				serializable_state[pos_key] = {
					"name": occupied_cells[pos]["name"],
					"color": {
						"r": occupied_cells[pos]["color"].r,
						"g": occupied_cells[pos]["color"].g,
						"b": occupied_cells[pos]["color"].b,
						"a": occupied_cells[pos]["color"].a
					}
				}
		
		# 保存到全局状态
		game_state.save_modification_state(serializable_state)
		print("已保存改装状态")
	else:
		print("未找到全局状态管理器，改装状态仅保存在内存中")

# 从全局状态加载改装状态
func load_modification_state():
	# 检查是否有全局游戏状态管理器
	var game_state = get_tree().get_first_node_in_group("game_state")
	if game_state and game_state.has_method("load_modification_state"):
		var loaded_state = game_state.load_modification_state()
		if loaded_state != null:
			# 清除现有状态（除了玩家位置）
			var player_pos_data = null
			if occupied_cells.has(player_position):
				player_pos_data = occupied_cells[player_position]
			
			occupied_cells.clear()
			
			# 恢复玩家位置
			if player_pos_data != null:
				occupied_cells[player_position] = player_pos_data
			
			# 加载保存的状态
			for pos_key in loaded_state.keys():
				# 将字符串键转回Vector2
				var coords = pos_key.split(",")
				var pos = Vector2(int(coords[0]), int(coords[1]))
				
				var mod_data = loaded_state[pos_key]
				var color = Color(
					mod_data["color"]["r"],
					mod_data["color"]["g"],
					mod_data["color"]["b"],
					mod_data["color"]["a"]
				)
				
				occupied_cells[pos] = {
					"name": mod_data["name"],
					"color": color
				}
			
			print("已从全局状态加载改装状态")
			
			# 更新网格显示
			update_grid_cells_from_occupied()
		else:
			print("没有找到保存的改装状态")
	else:
		print("未找到全局状态管理器，无法加载改装状态")

# 获取界面开启状态
func is_modification_screen_open() -> bool:
	return is_open 

# 获取当前装备的改装件
func get_equipped_modifications():
	# 返回当前占据的格子信息，但将Vector2键转换为字符串格式
	var result = {}
	for pos_key in occupied_cells.keys():
		# 将Vector2键转换为字符串格式
		var str_key = str(pos_key.x) + "," + str(pos_key.y)
		result[str_key] = occupied_cells[pos_key]
	return result

# 添加一个辅助函数，获取屏幕坐标
func get_viewport_position(global_position: Vector2) -> Vector2:
	var canvas = get_canvas_transform()
	var viewport_pos = canvas * global_position
	return viewport_pos

# 检查是否可以在指定位置放置改装件
func can_place_at_position(position: Vector2) -> bool:
	# 如果是玩家位置，不能放置
	if position == player_position:
		return false
	
	# 检查是否已经被占据
	if occupied_cells.has(position):
		return false
	
	# 检查是否与玩家直接相邻
	if is_adjacent_to_player(position):
		return true
	
	# 检查是否通过其他改装件与玩家相连
	return is_connected_to_player(position)

# 检查是否与玩家直接相邻
func is_adjacent_to_player(position: Vector2) -> bool:
	# 上下左右四个方向
	var directions = [
		Vector2(0, -1),  # 上
		Vector2(1, 0),   # 右
		Vector2(0, 1),   # 下
		Vector2(-1, 0)   # 左
	]
	
	# 检查四个方向
	for dir in directions:
		var neighbor = position + dir
		if neighbor == player_position:
			return true
	
	return false

# 检查是否通过其他改装件与玩家相连
func is_connected_to_player(position: Vector2) -> bool:
	# 广度优先搜索找到是否有路径连接到玩家
	var visited = {}
	var queue = []
	
	# 添加相邻的已占据格子作为起点
	var directions = [
		Vector2(0, -1),  # 上
		Vector2(1, 0),   # 右
		Vector2(0, 1),   # 下
		Vector2(-1, 0)   # 左
	]
	
	# 首先检查是否直接与玩家相邻
	for dir in directions:
		var neighbor = position + dir
		if neighbor == player_position:
			return true
	
	# 然后检查是否通过其他模块连接到玩家
	for dir in directions:
		var neighbor = position + dir
		if occupied_cells.has(neighbor):
			queue.append(neighbor)
			visited[neighbor] = true
	
	# 如果没有相邻的已占据格子，直接返回false
	if queue.size() == 0:
		return false
	
	# 广度优先搜索
	while queue.size() > 0:
		var current = queue.pop_front()
		
		# 如果找到玩家，返回true
		if current == player_position:
			return true
		
		# 检查相邻格子
		for dir in directions:
			var neighbor = current + dir
			
			# 如果已访问过或不在占据列表中，跳过
			if visited.has(neighbor) or not occupied_cells.has(neighbor):
				continue
			
			# 标记为已访问并添加到队列
			visited[neighbor] = true
			queue.append(neighbor)
	
	# 没有找到路径到玩家
	return false

# 修改mark_cell_occupied函数，移除视觉反馈
func mark_cell_occupied(position: Vector2, data):
	# 记录占据状态
	occupied_cells[position] = data
	
	# 注释掉视觉管理器相关代码
	# 获取数据管理器
	# var mod_data_manager = load("res://scenes/modifications/data/modification_data_manager.gd").get_instance()
	
	# 如果有视觉管理器且有改装件ID，添加视觉效果
	# if visual_manager and data.has("name"):
	# 	# 通过名称查找改装件ID
	# 	var mod_id = data["name"].to_lower().replace(" ", "_")
	# 	
	# 	# 从数据管理器获取改装件资源
	# 	var mod_resource = mod_data_manager.get_modification_data(mod_id)
	# 	
	# 	if mod_resource:
	# 		# 将改装件添加到视觉管理器
	# 		visual_manager.attach_mod(mod_resource, position)
	# 		print("修改界面: 已为改装件 " + mod_resource.name + " 添加视觉效果")
	# 	else:
	# 		push_warning("修改界面: 无法找到改装件资源: " + mod_id)

# 修改clear_cell_occupied函数，移除视觉反馈
func clear_cell_occupied(position: Vector2):
	# 获取当前改装件数据，用于后续视觉反馈
	var mod_data = null
	if occupied_cells.has(position):
		mod_data = occupied_cells[position]
		
		# 从占据状态中移除
		occupied_cells.erase(position)
		
		# 注释掉视觉管理器相关代码
		# 如果有视觉管理器且有改装件ID，移除视觉效果
		# if visual_manager and mod_data.has("name"):
		# 	# 通过名称查找改装件ID
		# 	var mod_id = mod_data["name"].to_lower().replace(" ", "_")
		# 	
		# 	# 从视觉管理器移除改装件
		# 	visual_manager.detach_mod(mod_id, position)
		# 	print("修改界面: 已移除改装件视觉效果，位置: " + str(position))

# 获取所有有效放置位置
func get_valid_placement_positions() -> Array:
	var valid_positions = []
	
	# 遍历所有格子
	for i in range(GRID_SIZE):
		for j in range(GRID_SIZE):
			var pos = Vector2(i, j)
			if can_place_at_position(pos):
				valid_positions.append(pos)
	
	return valid_positions

# 更新网格可放置状态视觉显示
func update_grid_placement_visuals():
	# 遍历所有网格单元格
	for i in range(grid_container.get_child_count()):
		var cell = grid_container.get_child(i)
		if cell.has_method("update_placement_status"):
			cell.update_placement_status()

# 检查如果移除一个位置的配件，会导致哪些配件失去连接
func get_disconnected_mods_if_removed(position: Vector2) -> Array:
	# 保存当前占据状态的副本
	var original_occupied = occupied_cells.duplicate(true)
	var disconnected_positions = []
	
	# 暂时移除这个位置
	if occupied_cells.has(position):
		occupied_cells.erase(position)
	
	# 检查所有当前占据的非玩家格子
	for pos in occupied_cells.keys():
		# 跳过玩家位置
		if occupied_cells[pos].has("is_player") and occupied_cells[pos]["is_player"]:
			continue
		
		# 检查是否仍然连接到玩家
		if not is_connected_to_player_from_position(pos):
			disconnected_positions.append(pos)
	
	# 恢复原始占据状态
	occupied_cells = original_occupied
	
	return disconnected_positions

# 检查特定位置是否连接到玩家（对现有is_connected_to_player的修改版本）
func is_connected_to_player_from_position(position: Vector2) -> bool:
	# 直接检查是否与玩家相邻
	if is_adjacent_to_player(position):
		return true
		
	# 广度优先搜索找到是否有路径连接到玩家
	var visited = {}
	var queue = []
	
	# 将当前位置添加到队列
	queue.append(position)
	visited[position] = true
	
	# 方向向量
	var directions = [
		Vector2(0, -1),  # 上
		Vector2(1, 0),   # 右
		Vector2(0, 1),   # 下
		Vector2(-1, 0)   # 左
	]
	
	# 广度优先搜索
	while queue.size() > 0:
		var current = queue.pop_front()
		
		# 检查相邻格子
		for dir in directions:
			var neighbor = current + dir
			
			# 如果是玩家位置，返回true
			if neighbor == player_position:
				return true
			
			# 如果已访问过或不在占据列表中，跳过
			if visited.has(neighbor) or not occupied_cells.has(neighbor):
				continue
			
			# 标记为已访问并添加到队列
			visited[neighbor] = true
			queue.append(neighbor)
	
	# 没有找到路径到玩家
	return false

# 收集失去连接的改装配件并返回到物品栏
func collect_disconnected_mods(disconnected_positions: Array) -> Array:
	var collected_mods = []
	
	# 收集所有失去连接的改装配件信息
	for pos in disconnected_positions:
		if occupied_cells.has(pos):
			var mod_data = occupied_cells[pos]
			collected_mods.append({
				"name": mod_data["name"],
				"color": mod_data["color"],
				"position": pos
			})
			
			# 从占据状态中移除
			occupied_cells.erase(pos)
	
	# 找到所有网格单元格并清除这些位置的改装配件
	for i in range(grid_container.get_child_count()):
		var cell = grid_container.get_child(i)
		for pos in disconnected_positions:
			if cell.grid_position == pos:
				cell.clear_modification_silent()  # 不更新全局占据状态
	
	# 更新网格可放置状态
	update_grid_placement_visuals()
	
	return collected_mods

# 将改装配件添加回物品栏 - 使用新的物品栏系统
func add_mods_to_inventory(mods: Array):
	var mod_inventory = ModInventoryClass.get_instance()
	
	for mod_data in mods:
		# 使用新的物品栏添加改装件
		# 假设mod_data包含配件ID或名称等信息
		if mod_data.has("name"):
			# 这里可能需要一个ID到名称的映射
			# 先简单实现，假设名称就是ID
			var mod_id = mod_data["name"].to_lower().replace(" ", "_")
			mod_inventory.add_modification(mod_id, 1)
			
			print("已将拖拽中的道具 [" + mod_data["name"] + "] 添加回物品栏")
			
			# 刷新物品栏显示
			if mod_inventory_panel and mod_inventory_panel.has_method("refresh"):
				mod_inventory_panel.refresh()

# 将拖拽中的道具添加回物品栏 - 使用新的物品栏系统
func add_dragged_mod_to_inventory(mod_name: String, mod_color: Color):
	# 使用新的物品栏添加改装件
	var mod_id = mod_name.to_lower().replace(" ", "_")
	var mod_inventory = ModInventoryClass.get_instance()
	mod_inventory.add_modification(mod_id, 1)
	
	print("已将拖拽中的道具 [" + mod_name + "] 添加回物品栏")
	
	# 刷新物品栏显示
	if mod_inventory_panel and mod_inventory_panel.has_method("refresh"):
		mod_inventory_panel.refresh()

# 捕获未能放置在有效网格的拖拽道具
func _can_drop_data(_position, data):
	# 只要是改装件数据都接受，确保任何地方松开鼠标都能处理
	if data is Dictionary and data.has("type") and data["type"] == "modification_part_placeholder":
		print("拖拽道具可以放置在此区域")
		return true
	return false

# 处理拖放数据
func _drop_data(_position, data):
	# 任何拖拽的改装件数据，如果没有放在有效位置，立即回收到物品栏
	if data is Dictionary and data.has("type") and data["type"] == "modification_part_placeholder":
		if data.has("name") and data.has("color"):
			# 检查这个道具是否已经被放置在网格中的其他位置
			var already_placed = false
			for i in range(grid_container.get_child_count()):
				var cell = grid_container.get_child(i)
				if cell.current_mod != null and cell.current_mod["name"] == data["name"] and cell.current_mod["color"] == data["color"]:
					already_placed = true
					break
			
			# 只有当道具未被放置时才添加到物品栏
			if not already_placed:
				add_dragged_mod_to_inventory(data["name"], data["color"])
				print("已将拖拽道具回收到物品栏: [" + data["name"] + "]")

# 处理输入事件，确保能捕获到拖拽释放
func _input(event):
	# 只在界面开启时处理
	if not is_open:
		return
		
	# 检测鼠标释放事件
	if event is InputEventMouseButton:
		var mouse_event = event as InputEventMouseButton
		# 检测左键释放，可能是拖拽结束
		if mouse_event.button_index == MOUSE_BUTTON_LEFT and not mouse_event.pressed:
			# 延迟一帧检查是否有未被接收的拖拽物品
			await get_tree().process_frame
			
			# 检查是否有全局拖拽跟踪器
			if get_tree().root.has_node("GlobalDragTracker"):
				var tracker = get_tree().root.get_node("GlobalDragTracker")
				var drag_data = tracker.get_meta("drag_data") if tracker.has_meta("drag_data") else null
				
				if drag_data != null and drag_data.has("name") and drag_data.has("color"):
					# 检查这个道具是否已经被放置在网格中
					var already_placed = false
					for i in range(grid_container.get_child_count()):
						var cell = grid_container.get_child(i)
						if cell.current_mod != null and cell.current_mod["name"] == drag_data["name"] and cell.current_mod["color"] == drag_data["color"]:
							already_placed = true
							break
					
					# 如果未被放置，添加到物品栏
					if not already_placed:
						add_dragged_mod_to_inventory(drag_data["name"], drag_data["color"])
						print("全局监测：已将未放置的拖拽道具添加回物品栏")
				
				# 清理全局跟踪器
				tracker.queue_free()

# 检查拖动操作，处理连接断开的情况
func handle_mod_drag(from_position: Vector2, keep_dragged_mod: bool = false):
	# 获取如果移除这个位置会导致哪些配件失去连接
	var disconnected_positions = get_disconnected_mods_if_removed(from_position)
	
	# 如果需要保留被拖拽的道具，确保from_position不在disconnected_positions中
	if keep_dragged_mod and disconnected_positions.has(from_position):
		disconnected_positions.erase(from_position)
	
	# 保存当前被拖拽道具的信息，以便后续添加到物品栏
	var dragged_mod_data = null
	if occupied_cells.has(from_position):
		dragged_mod_data = occupied_cells[from_position].duplicate()
		dragged_mod_data["position"] = from_position
	
	if disconnected_positions.size() > 0:
		print("拖走此配件会导致 " + str(disconnected_positions.size()) + " 个配件失去连接")
		
		# 收集失去连接的改装配件
		var collected_mods = collect_disconnected_mods(disconnected_positions)
		
		# 将这些改装配件添加回物品栏
		add_mods_to_inventory(collected_mods)
	
	# 无论是否有断开连接的配件，都清除被拖拽的配件
	if keep_dragged_mod and dragged_mod_data != null:
		# 找到并清除被拖拽格子的内容
		for i in range(grid_container.get_child_count()):
			var cell = grid_container.get_child(i)
			if cell.grid_position == from_position:
				cell.clear_modification_silent()
				# 清除占据状态
				clear_cell_occupied(from_position)
				print("清除格子 " + str(from_position) + " 的配件: [" + dragged_mod_data["name"] + "]")
				break
	
	return disconnected_positions.size() > 0
